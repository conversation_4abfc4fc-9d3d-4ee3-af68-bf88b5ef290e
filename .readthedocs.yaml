# .readthedocs.yaml
# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

# Set the version of Python and other tools you might need
build:
    os: ubuntu-20.04
    tools:
        python: "3.9"

# Build documentation in the docs/ directory with Mkdocs
mkdocs:
    configuration: mkdocs.yml
    fail_on_warning: false

# Optionally declare the Python requirements required to build your docs
# RTD does not support installation of prctl which is a requirement for the package. To update requirements.txt in docs/
# do (1) uncomment line 47,48 in setup, in up-req-files. Do not forget do undo (1) once finished generating the file.
python:
    install:
        - requirements: docs/requirements.txt