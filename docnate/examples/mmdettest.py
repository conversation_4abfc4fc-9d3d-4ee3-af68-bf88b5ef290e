import docnate as dd
from docnate.extern.mmdet_wrapper import <PERSON><PERSON>etDetector
from docnate.datapoint.view import IMAGE_DEFAULTS, Layout
from docnate.pipe import TextExtractionService, TextOrderService, PageParsingService
from docnate.extern.tessocr import TesseractOcrDetector
from docnate.utils.settings import Relationships
from docnate.pipe.common import IntersectionMatcher, FamilyCompound
import numpy as np
import matplotlib.pyplot as plt
# Define your model paths and device
model_name = '/home/<USER>/Downloads/config.py'
checkpoint = '/home/<USER>/Downloads/model.pth'
device = 'cpu'
def show_image(page) :
    image = page.viz()
    if image is not None:
        plt.figure(figsize=(25, 17))
        plt.axis('off')
        plt.imshow(image)
        plt.show()  # Ensure the plot window opens
    else:
        print("No image generated.")

@dd.object_types_registry.register("MMTYPE")
class MMEXTENSION(dd.ObjectTypes):
    """Additional YOLO labels not registered yet"""

    MM_CU_INVOICE_NO = 'cu_invoice_no',
    MM_INOVICE_DATE = 'invoice_date',
    MM_INVOICE_No = 'invoice_no',
    MM_LPO = 'lpo',
    MM_TAX_DATE = 'tax_date',
    MM_TOTAL = 'total',
    MM_VAT = 'vat',

IMAGE_DEFAULTS.IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in MMEXTENSION})

mm_categories = {
        1:  MMEXTENSION.MM_CU_INVOICE_NO,
        2:  MMEXTENSION.MM_INOVICE_DATE,
        3:  MMEXTENSION.MM_INVOICE_No,
        4:  MMEXTENSION.MM_LPO,
        5:  MMEXTENSION.MM_TAX_DATE,
        6:  MMEXTENSION.MM_TOTAL,
        7:  MMEXTENSION.MM_VAT,
    }
dd.ModelCatalog.register(model_name, dd.ModelProfile(
    name=model_name,
    description="mm model for table detection",
    tp_model=False,
    size=[],
    categories=mm_categories,
    model_wrapper="MMDetDetector"
))

categories = dd.ModelCatalog.get_profile(model_name).categories
categories_names_from_model = [layout_item for layout_item in list(categories.values())]

# Initialize the MMDetObjectDetector
mmdet_detector = MMDetDetector(
    model_name=model_name,
    checkpoint=checkpoint,
    categories=mm_categories,
    device=device
)

# Create an ImageLayoutService with the MMDetection detector
layout_service = dd.ImageLayoutService(mmdet_detector, to_image=True, crop_image=True)

# Create a pipeline
# Initialize OCR and text ordering components
tesseract_config_path = '/home/<USER>/docnate/configs/conf_tesseract.yaml'
ocr_detector = TesseractOcrDetector(path_yaml=tesseract_config_path)
text_service = TextExtractionService(ocr_detector, extract_from_roi=list(mm_categories.values()))
# Initialize other components like the matching service and text order service
matcher = IntersectionMatcher(matching_rule='ioa', threshold=0.3, max_parent_only=True)
family_compound = FamilyCompound(relationship_key=Relationships.CHILD,
                                 parent_categories=categories_names_from_model,
                                 child_categories=[dd.LayoutType.WORD])
map_comp = dd.MatchingService(family_compounds=[family_compound], matcher=matcher)
text_order_comp = dd.TextOrderService(text_container=dd.LayoutType.WORD,
                                    #  text_block_categories= categories_names_from_model,
                                     floating_text_block_categories=categories_names_from_model,
                                     include_residual_text_container=True)
 

page_parsing_service = PageParsingService(text_container=dd.LayoutType.WORD,
                                          floating_text_block_categories=categories_names_from_model,
                                     include_residual_text_container=False)


pipe_comp_list = [layout_service,text_service, map_comp, text_order_comp]
analyzer = dd.DoctectionPipe(pipeline_component_list=pipe_comp_list, page_parsing_service=page_parsing_service)

img_path = '/home/<USER>/docnate/images'
df = analyzer.analyze(path=img_path)

df.reset_state()
cnt = 0
for dp in df :
    cnt=cnt+1
    print(f"PAGE : {cnt}")
    # print(dp)
    show_image(dp)
    for category_name in mm_categories.values():
        annotations = dp.get_annotation(category_names=category_name)
        for ann in annotations:
            if ann.text:
                print(f"{ann.category_name.upper()}: {ann.text}")
                print()

