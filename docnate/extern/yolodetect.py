# -*- coding: utf-8 -*-
# File: yolodetect.py

# Copyright 2021 Dr<PERSON> <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
YOLO models using ultralytics library for object detection
"""

from __future__ import annotations

from abc import ABC
from pathlib import Path
from typing import Mapping, Optional, Sequence

from lazy_imports import try_import

from ..utils.file_utils import get_pytorch_requirement, get_ultralytics_requirement
from ..utils.settings import DefaultType, ObjectTypes, TypeOrStr, get_type
from ..utils.types import PathLikeOrStr, PixelValues, Requirement
from .base import DetectionResult, ModelCategories, ObjectDetector

with try_import() as ultralytics_import_guard:
    from ultralytics import YOLO  # pylint: disable=W0611


def _yolo_to_detectresult(results) -> list[DetectionResult]:
    """
    Converts YOLO detection results into DetectionResult objects.

    Args:
        results: YOLO detection results

    Returns:
        A list of DetectionResult objects
    """

    all_results: list[DetectionResult] = []

    # Loop through each detected box
    for box in results.boxes:
        # Extract bounding box coordinates
        x1, y1, x2, y2 = box.xyxy.tolist()[0]

        # Get class ID and confidence score (YOLO uses 0-based indexing)
        class_id = int(box.cls)
        confidence = float(box.conf)

        # Create a DetectionResult object
        detection = DetectionResult(
            box=[x1, y1, x2, y2],
            score=confidence,
            class_id=class_id,
        )

        # Append the DetectionResult to the list
        all_results.append(detection)

    return all_results


def yolo_predict_image(
    np_img: PixelValues,
    model: YOLO,
    conf_threshold: float,
    iou_threshold: float,
) -> list[DetectionResult]:
    """
    Run inference using the YOLO model.

    Args:
        np_img: Input image as numpy array (BGR format)
        model: YOLO model instance
        conf_threshold: Confidence threshold for detections
        iou_threshold: Intersection-over-Union threshold for non-max suppression

    Returns:
        A list of detection results
    """
    # Run the model
    results = model(source=np_img, conf=conf_threshold, iou=iou_threshold, verbose=False)[0]

    # Convert results to DetectionResult format
    all_results = _yolo_to_detectresult(results)

    return all_results


class YoloDetectorMixin(ObjectDetector, ABC):
    """
    Base class for YOLO detector implementation. This class only implements the basic wrapper functions.
    """

    def __init__(
        self,
        categories: Mapping[int, TypeOrStr],
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ):
        """
        Args:
            categories: A dict with key (indices) and values (category names).
                       Note: YOLO class indices start from 0, but they will be shifted by 1
                       to match docnate convention (where 0 is reserved for background).
            filter_categories: The model might return objects that are not supposed to be predicted
                              and that should be filtered. Pass a list of category names that must
                              not be returned.
        """

        self.categories = ModelCategories(init_categories=categories)
        if filter_categories:
            self.categories.filter_categories = tuple(get_type(cat) for cat in filter_categories)

    def _map_category_names(self, detection_results: list[DetectionResult]) -> list[DetectionResult]:
        """
        Populating category names to `DetectionResult`s

        Args:
            detection_results: list of `DetectionResult`s. Will also filter categories

        Returns:
            List of `DetectionResult`s with attribute `class_name` populated
        """
        filtered_detection_result: list[DetectionResult] = []
        shifted_categories = self.categories.shift_category_ids(shift_by=-1)
        for result in detection_results:
            result.class_name = shifted_categories.get(
                result.class_id if result.class_id is not None else -1, DefaultType.DEFAULT_TYPE
            )
            if result.class_name != DefaultType.DEFAULT_TYPE:
                if result.class_id is not None:
                    result.class_id += 1
                    filtered_detection_result.append(result)
        return filtered_detection_result

    def get_category_names(self) -> tuple[ObjectTypes, ...]:
        """Get the category names"""
        return self.categories.get_categories(as_dict=False)

    @staticmethod
    def get_name(path_weights: PathLikeOrStr) -> str:
        """
        Returns the name of the model based on the weights path.

        Args:
            path_weights: Path to the model weights

        Returns:
            Model name string
        """
        return "yolo_" + "_".join(Path(path_weights).parts[-2:])


class YoloDetector(YoloDetectorMixin):
    """
    Document detector using YOLO engine for layout analysis.

    This wrapper uses the ultralytics library to run YOLO models for object detection.
    It supports various YOLO versions (YOLOv5, YOLOv8, YOLOv10, etc.) that are compatible
    with the ultralytics library.

    The detector predicts different categories of document elements such as text, tables,
    figures, headers, etc.

    Example:
        ```python
        from docnate.extern import YoloDetector
        from docnate.extern.model import ModelCatalog

        categories = {
            1: "text",
            2: "title",
            3: "list",
            4: "table",
            5: "figure"
        }

        detector = YoloDetector(
            path_weights="path/to/yolo/weights.pt",
            categories=categories,
            conf_threshold=0.5,
            iou_threshold=0.4
        )

        detection_results = detector.predict(bgr_image_np_array)
        ```
    """

    def __init__(
        self,
        path_weights: PathLikeOrStr,
        categories: Mapping[int, TypeOrStr],
        conf_threshold: float = 0.5,
        iou_threshold: float = 0.4,
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ) -> None:
        """
        Initialize the YOLO detector.

        Args:
            path_weights: Path to the YOLO model weights file (.pt file).
            categories: A dict with key (indices) and values (category names).
                       Note: YOLO class indices start from 0, but they will be shifted by 1
                       to match docnate convention (where 0 is reserved for background).
            conf_threshold: Confidence threshold for YOLO detections. Default: 0.5
            iou_threshold: IoU threshold for YOLO detections. Default: 0.4
            filter_categories: The model might return objects that are not supposed to be predicted
                              and that should be filtered. Pass a list of category names that must
                              not be returned.
        """
        super().__init__(categories, filter_categories)

        self.path_weights = Path(path_weights)
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold

        self.name = self.get_name(self.path_weights)
        self.model_id = self.get_model_id()

        # Load YOLO model with specified weights
        self.model = YOLO(self.path_weights)

    def predict(self, np_img: PixelValues) -> list[DetectionResult]:
        """
        Perform inference on a document image using YOLO and return detection results.

        Args:
            np_img: Input image as numpy array (BGR format)

        Returns:
            A list of DetectionResult objects.
        """
        detection_results = yolo_predict_image(
            np_img,
            self.model,
            self.conf_threshold,
            self.iou_threshold,
        )
        return self._map_category_names(detection_results)

    @classmethod
    def get_requirements(cls) -> list[Requirement]:
        """
        Get the requirements for running the YOLO detector.

        Returns:
            List of requirements (PyTorch and ultralytics)
        """
        return [get_pytorch_requirement(), get_ultralytics_requirement()]

    def clone(self) -> YoloDetector:
        """
        Clone the current detector instance.

        Returns:
            A new instance of YoloDetector with the same configuration.
        """
        return self.__class__(
            path_weights=self.path_weights,
            categories=self.categories.get_categories(),
            conf_threshold=self.conf_threshold,
            iou_threshold=self.iou_threshold,
            filter_categories=self.categories.filter_categories,
        )

    @staticmethod
    def get_wrapped_model(path_weights: PathLikeOrStr) -> YOLO:
        """
        Get the wrapped YOLO model. Useful if one does not want to build the wrapper
        but only needs the instantiated model.

        Args:
            path_weights: Path to the YOLO model weights file.

        Returns:
            YOLO model instance
        """
        return YOLO(path_weights)

    def clear_model(self) -> None:
        """
        Clear the inner model. Needed for model updates during training.
        """
        self.model = None

