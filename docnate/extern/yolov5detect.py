# -*- coding: utf-8 -*-
# File: yolov5_detector.py

from __future__ import annotations
from abc import ABC
from pathlib import Path
from typing import Mapping, Optional, Sequence, List

from typing import Any, Literal, Mapping, Optional, Union
import numpy as np
from lazy_imports import try_import

from ..utils.error import DependencyError
from ..utils.file_utils import get_pytorch_requirement
from ..utils.settings import DefaultType, ObjectTypes, TypeOrStr, get_type
from ..utils.types import PathLikeOrStr, PixelValues, Requirement
from .base import DetectionResult, ModelCategories, ObjectDetector

with try_import() as torch_import_guard:
    import torch
    from PIL import Image


class YoloV5DetectorMixin(ObjectDetector, ABC):
    """
    Base class for YOLOv5 detector wrapper (like YoloDetector).
    """

    def __init__(
        self,
        categories: Mapping[int, TypeOrStr],
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ):
        self.categories = ModelCategories(init_categories=categories)
        if filter_categories:
            self.categories.filter_categories = tuple(get_type(cat) for cat in filter_categories)

    def _map_category_names(self, detection_results: List[DetectionResult]) -> List[DetectionResult]:
        """
        Populate category names and apply filtering.

        Args:
            detection_results: list of DetectionResult

        Returns:
            List of DetectionResult with class_name filled and filtered
        """
        filtered_detection_result: List[DetectionResult] = []
        categories_map = self.categories.get_categories(as_dict=True)
        for result in detection_results:
            result.class_name = categories_map.get(
                result.class_id if result.class_id is not None else -1,
                DefaultType.DEFAULT_TYPE,
            )
            if result.class_name != DefaultType.DEFAULT_TYPE:
                filtered_detection_result.append(result)
        return filtered_detection_result

    def get_category_names(self) -> tuple[ObjectTypes, ...]:
        return self.categories.get_categories(as_dict=False)

    @staticmethod
    def get_name(path_weights: PathLikeOrStr) -> str:
        return "yolov5_" + "_".join(Path(path_weights).parts[-2:])


def _get_yolov5_requirements() -> list[Requirement]:
    """Get requirements for YOLOv5"""
    return [get_pytorch_requirement()]


def _yolov5_to_detectresult(results, categories: ModelCategories) -> list[DetectionResult]:
    """
    Converts YOLOv5 detection results into DetectionResult objects.

    :param results: YOLOv5 detection results object
    :param categories: ModelCategories instance for YOLOv5 classes.
    :return: A list of DetectionResult objects
    """
    all_results: list[DetectionResult] = []
    
    categories_map = categories.get_categories(as_dict=True)
    
    # YOLOv5 results.xyxy[0] contains detections for the first image
    if len(results.xyxy[0]) > 0:
        for detection in results.xyxy[0]:
            x1, y1, x2, y2, conf, cls = detection.tolist()
            class_id_0_indexed = int(cls)
            confidence = float(conf)
            
            # Convert 0-indexed class_id to 1-indexed for docnate
            class_id = class_id_0_indexed + 1
            class_name = categories_map.get(class_id)
            if class_name is None:
                class_name = DefaultType.DEFAULT_TYPE
            
            detection_result = DetectionResult(
                box=[x1, y1, x2, y2],
                score=confidence,
                class_id=class_id,
                absolute_coords=True,
                class_name=class_name
            )
            all_results.append(detection_result)
    
    return all_results


class YoloV5Detector(YoloV5DetectorMixin):
    """
    A docnate wrapper for YOLOv5 object detection models using torch.hub.
    """

    def __init__(
        self,
        path_weights: PathLikeOrStr,
        categories: Mapping[int, TypeOrStr],
        device: Optional[Union[Literal["cpu", "cuda"], Any]] = None,
        conf_threshold: float = 0.1,
        iou_threshold: float = 0.5,
        max_det: int = 1000,
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ) -> None:
        """
        Args:
            path_weights: Path to the YOLOv5 model weights (e.g., yolov5s.pt)
            categories: A dict with the model output label and value
            device: "cpu" or "cuda" or any torch.device.
            conf_threshold: Confidence threshold for object detection.
            iou_threshold: IoU threshold for non-maximum suppression.
            max_det: Maximum number of detections per image.
            filter_categories: Categories to filter.
        """
        super().__init__(categories, filter_categories)

        self.path_weights = Path(path_weights)
        self.device = device
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.max_det = max_det

        self.name = self.get_name(self.path_weights)
        self.model_id = self.get_model_id()

        self.model = self.get_wrapped_model(self.path_weights, self.device)
        
        # Set model parameters
        self.model.conf = self.conf_threshold
        self.model.iou = self.iou_threshold
        self.model.max_det = self.max_det

    def predict(self, np_img: PixelValues) -> list[DetectionResult]:
        """
        Prediction per image.

        Args:
            np_img: image as `np.array`

        Returns:
            A list of `DetectionResult`
        """
        # Ensure image is in uint8 format
        if np_img.dtype != np.uint8:
            image_array_uint8 = (np_img * 255).astype(np.uint8)
        else:
            image_array_uint8 = np_img

        # Convert numpy array to PIL Image
        # YOLOv5 expects RGB format, but we need to check if input is BGR
        if len(image_array_uint8.shape) == 3 and image_array_uint8.shape[2] == 3:
            # Assume input is BGR (OpenCV format), convert to RGB
            image_array_rgb = image_array_uint8[:, :, ::-1]
        else:
            image_array_rgb = image_array_uint8
            
        pil_image = Image.fromarray(image_array_rgb)
        
        # Run inference
        results = self.model(pil_image)
        
        return self._map_category_names(_yolov5_to_detectresult(results, self.categories))

    @classmethod
    def get_requirements(cls) -> list[Requirement]:
        return _get_yolov5_requirements()

    def clone(self) -> YoloV5Detector:
        return self.__class__(
            self.path_weights,
            self.categories.get_categories(),
            self.device,
            self.conf_threshold,
            self.iou_threshold,
            self.max_det,
            self.categories.filter_categories,
        )

    @staticmethod
    def get_wrapped_model(path_weights: PathLikeOrStr, device: Optional[Union[Literal["cpu", "cuda"], Any]]):
        """
        Get the inner (wrapped) YOLOv5 model using torch.hub.

        Args:
            path_weights: Path to the YOLOv5 model weights
            device: "cpu" or "cuda" or any torch.device.

        Returns:
            The YOLOv5 model instance.
        """
        model = torch.hub.load(
            'ultralytics/yolov5',
            'custom',
            path=str(path_weights),
            trust_repo=True
        )
        
        if device:
            model.to(device)
            
        return model

    def clear_model(self) -> None:
        self.model = None
