# -*- coding: utf-8 -*-
# File: paddleocr.py

# Copyright 2021 Dr<PERSON> <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
PaddleOCR wrapper for text detection and recognition
"""

from __future__ import annotations

from abc import ABC
from typing import Mapping, Optional, Sequence

from lazy_imports import try_import

from ..utils.file_utils import get_paddleocr_requirement, get_paddlepaddle_requirement
from ..utils.settings import DefaultType, ObjectTypes, TypeOrStr, get_type
from ..utils.types import PixelValues, Requirement
from .base import DetectionResult, ModelCategories, ObjectDetector

with try_import() as paddleocr_import_guard:
    from paddleocr import PaddleOCR


def _paddleocr_to_detectresult(results) -> list[DetectionResult]:
    """
    Converts PaddleOCR detection results into DetectionResult objects.

    Args:
        results: PaddleOCR detection results

    Returns:
        A list of DetectionResult objects
    """
    all_results: list[DetectionResult] = []

    if not results:
        return all_results

    # PaddleOCR returns a list of results for each detected text region
    # Each result contains: [[[x1, y1], [x2, y2], [x3, y3], [x4, y4]], (text, confidence)]
    for line in results:
        if line is not None and len(line) >= 2:

            # Extract bounding box coordinates (4 corner points)
            bbox_points = line[0]
            text_info = line[1]
            
            if len(text_info) >= 2:
                text = text_info[0]
                confidence = float(text_info[1])
                
                # Convert 4-point polygon to bounding box [x1, y1, x2, y2]
                x_coords = [point[0] for point in bbox_points]
                y_coords = [point[1] for point in bbox_points]
                
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)

                # Create a DetectionResult object
                detection = DetectionResult(
                    box=[x1, y1, x2, y2],
                    score=confidence,
                    class_id=1,  # Default to word class
                    text=text,
                    absolute_coords=True,
                )

                all_results.append(detection)

    return all_results


def paddleocr_predict_image(
    np_img: PixelValues,
    model: PaddleOCR,
    use_angle_cls: bool = True,
) -> list[DetectionResult]:
    """
    Run inference using the PaddleOCR model.

    Args:
        np_img: Input image as numpy array (BGR format)
        model: PaddleOCR model instance
        use_angle_cls: Whether to use angle classification

    Returns:
        A list of detection results
    """
    # PaddleOCR expects RGB format, cv2.imread reads in BGR
    import cv2
    img_rgb = cv2.cvtColor(np_img, cv2.COLOR_BGR2RGB)

    # Run the model
    results = model.ocr(img_rgb, cls=use_angle_cls)

    # Handle the case where results is a list of lists (for multiple images)
    if isinstance(results, list) and len(results) > 0:
        if isinstance(results[0], list):
            # Single image case - results[0] contains the actual results
            results = results[0]

    # Convert results to DetectionResult format
    all_results = _paddleocr_to_detectresult(results)

    return all_results


class PaddleOcrDetectorMixin(ObjectDetector, ABC):
    """
    Base class for PaddleOCR detector implementation. This class only implements the basic wrapper functions.
    """

    def __init__(
        self,
        categories: Mapping[int, TypeOrStr],
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ):
        """
        Args:
            categories: A dict with key (indices) and values (category names).
            filter_categories: The model might return objects that are not supposed to be predicted
                              and that should be filtered. Pass a list of category names that must
                              not be returned.
        """

        self.categories = ModelCategories(init_categories=categories)
        if filter_categories:
            self.categories.filter_categories = tuple(get_type(cat) for cat in filter_categories)

    def _map_category_names(self, detection_results: list[DetectionResult]) -> list[DetectionResult]:
        """
        Populating category names to `DetectionResult`s

        Args:
            detection_results: list of `DetectionResult`s. Will also filter categories

        Returns:
            List of `DetectionResult`s with attribute `class_name` populated
        """
        filtered_detection_result: list[DetectionResult] = []
        categories_dict = self.categories.get_categories(as_dict=True)
        
        for result in detection_results:
            result.class_name = categories_dict.get(
                result.class_id if result.class_id is not None else -1, DefaultType.DEFAULT_TYPE
            )
            if result.class_name != DefaultType.DEFAULT_TYPE:
                filtered_detection_result.append(result)
        return filtered_detection_result

    def get_category_names(self) -> tuple[ObjectTypes, ...]:
        """Get the category names"""
        return self.categories.get_categories(as_dict=False)

    @staticmethod
    def get_name(lang: str = "en", use_gpu: bool = False) -> str:
        """
        Returns the name of the model based on configuration.

        Args:
            lang: Language code for OCR
            use_gpu: Whether GPU is used

        Returns:
            Model name string
        """
        gpu_suffix = "_gpu" if use_gpu else "_cpu"
        return f"paddleocr_{lang}{gpu_suffix}"


class PaddleOcrDetector(PaddleOcrDetectorMixin):
    """
    Text detector using PaddleOCR engine for OCR.

    This wrapper uses the PaddleOCR library to run text detection and recognition.
    PaddleOCR supports multiple languages and provides both text detection and recognition
    in a single pipeline.

    The detector predicts text regions and extracts the text content with confidence scores.

    Example:
        ```python
        from docnate.extern import PaddleOcrDetector

        categories = {
            1: "word"
        }

        detector = PaddleOcrDetector(
            categories=categories,
            lang="en",
            use_angle_cls=True,
            use_gpu=False
        )

        detection_results = detector.predict(bgr_image_np_array)
        ```
    """

    def __init__(
        self,
        categories: Mapping[int, TypeOrStr],
        lang: str = "en",
        use_angle_cls: bool = True,
        use_gpu: bool = False,
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ) -> None:
        """
        Initialize the PaddleOCR detector.

        Args:
            categories: A dict with key (indices) and values (category names).
            lang: Language code for OCR (e.g., 'en', 'ch', 'fr', etc.). Default: 'en'
            use_angle_cls: Whether to use angle classification for text orientation. Default: True
            use_gpu: Whether to use GPU for inference. Default: False
            filter_categories: The model might return objects that are not supposed to be predicted
                              and that should be filtered. Pass a list of category names that must
                              not be returned.
        """
        super().__init__(categories, filter_categories)

        self.lang = lang
        self.use_angle_cls = use_angle_cls
        self.use_gpu = use_gpu

        self.name = self.get_name(self.lang, self.use_gpu)
        self.model_id = self.get_model_id()

        # Initialize PaddleOCR model
        self.model = PaddleOCR(
            use_angle_cls=self.use_angle_cls,
            lang=self.lang,
            use_gpu=self.use_gpu,
            show_log=False
        )

    def predict(self, np_img: PixelValues) -> list[DetectionResult]:
        """
        Perform inference on a document image using PaddleOCR and return detection results.

        Args:
            np_img: Input image as numpy array (BGR format)

        Returns:
            A list of DetectionResult objects.
        """
        detection_results = paddleocr_predict_image(
            np_img,
            self.model,
            self.use_angle_cls,
        )
        return self._map_category_names(detection_results)

    @classmethod
    def get_requirements(cls) -> list[Requirement]:
        """
        Get the requirements for running the PaddleOCR detector.

        Returns:
            List of requirements (PaddleOCR and PaddlePaddle)
        """
        return [get_paddleocr_requirement(), get_paddlepaddle_requirement()]

    def clone(self) -> PaddleOcrDetector:
        """
        Clone the current detector instance.

        Returns:
            A new instance of PaddleOcrDetector with the same configuration.
        """
        return self.__class__(
            categories=self.categories.get_categories(),
            lang=self.lang,
            use_angle_cls=self.use_angle_cls,
            use_gpu=self.use_gpu,
            filter_categories=self.categories.filter_categories,
        )

    @staticmethod
    def get_wrapped_model(
        lang: str = "en",
        use_angle_cls: bool = True,
        use_gpu: bool = False
    ) -> PaddleOCR:
        """
        Get the wrapped PaddleOCR model. Useful if one does not want to build the wrapper
        but only needs the instantiated model.

        Args:
            lang: Language code for OCR
            use_angle_cls: Whether to use angle classification
            use_gpu: Whether to use GPU

        Returns:
            PaddleOCR model instance
        """
        return PaddleOCR(
            use_angle_cls=use_angle_cls,
            lang=lang,
            use_gpu=use_gpu,
            show_log=False
        )

    def clear_model(self) -> None:
        """
        Clear the inner model. Needed for model updates during training.
        """
        self.model = None
