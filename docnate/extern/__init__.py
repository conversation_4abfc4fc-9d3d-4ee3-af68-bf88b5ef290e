# -*- coding: utf-8 -*-
# File: __init__.py

# Copyright 2021 Dr<PERSON> <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
# Wrappers for models of external libraries
"""

from .base import *
from .d2detect import *
from .deskew import *
from .doctrocr import *
from .fastlang import *
from .hfdetr import *
from .hflayoutlm import *
from .hflm import *
from .model import *
from .paddleocr import *
from .pdftext import *
from .tessocr import *
from .texocr import *  # type: ignore
from .tpdetect import *
from .mmdet_wrapper import *
from .yolodetect import *
from .yolov5detect import *
