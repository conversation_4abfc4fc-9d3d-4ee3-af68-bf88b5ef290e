# -*- coding: utf-8 -*-
# File: mmdetdetect.py

# Copyright 2021 Dr<PERSON> <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
MMDetection models wrapper for object detection.
"""

from __future__ import annotations

from abc import ABC
from pathlib import Path
from typing import Mapping, Optional, Sequence, Union, Literal

import numpy as np
from lazy_imports import try_import

from ..utils.error import DependencyError
from ..utils.file_utils import (
    get_pytorch_requirement,
    get_mmdet_requirement,
    mmdet_available,
    pytorch_available,
)
from ..utils.settings import DefaultType, ObjectTypes, TypeOrStr, get_type
from ..utils.types import PathLikeOrStr, PixelValues, Requirement
from .base import DetectionResult, ModelCategories, ObjectDetector
from .pt.ptutils import get_torch_device

with try_import() as mmdet_import_guard:
    from mmdet.apis import DetInferencer
    import torch
    import mmengine.runner.checkpoint as checkpoint_runner


def _mmdet_to_detectresult(result_dict) -> list[DetectionResult]:
    """
    Converts MMDetection results into DetectionResult objects.

    Args:
        result_dict: Dictionary output from MMDetection inferencer.

    Returns:
        A list of DetectionResult objects.
    """
    detection_results: list[DetectionResult] = []

    predictions_list = result_dict.get("predictions", [])
    if predictions_list:
        predictions = predictions_list[0]  # assume batch size 1
        for bbox, label, score in zip(predictions["bboxes"], predictions["labels"], predictions["scores"]):
            detection_results.append(
                DetectionResult(
                    box=bbox,
                    class_id=int(label),
                    score=float(score),
                    absolute_coords=True,
                )
            )

    return detection_results


def mmdet_predict_image(
    np_img: PixelValues,
    inferencer: DetInferencer,
) -> list[DetectionResult]:
    """
    Run inference using the MMDetection model.

    Args:
        np_img: Input image as numpy array (BGR format)
        inferencer: MMDetection DetInferencer instance

    Returns:
        A list of DetectionResult objects.
    """
    result = inferencer(np_img)
    return _mmdet_to_detectresult(result)


class MMDetDetectorMixin(ObjectDetector, ABC):
    """
    Base mixin class for MMDetection detectors implementing
    common category handling and naming utilities.
    """

    def __init__(
        self,
        categories: Mapping[int, TypeOrStr],
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ):
        self.categories = ModelCategories(init_categories=categories)
        if filter_categories:
            self.categories.filter_categories = tuple(get_type(cat) for cat in filter_categories)

    def _map_category_names(self, detection_results: list[DetectionResult]) -> list[DetectionResult]:
        """
        Populating category names to `DetectionResult`s

        Args:
            detection_results: list of `DetectionResult`s

        Returns:
            List of `DetectionResult`s with attribute `class_name` populated
        """
        filtered_detection_result: list[DetectionResult] = []
        for result in detection_results:
            result.class_name = self.categories.get_categories(as_dict=True).get(
                result.class_id, DefaultType.DEFAULT_TYPE
            )
            if result.class_name != DefaultType.DEFAULT_TYPE:
                filtered_detection_result.append(result)
        return filtered_detection_result

    def get_category_names(self) -> tuple[ObjectTypes, ...]:
        """Get the category names."""
        return self.categories.get_categories(as_dict=False)

    @staticmethod
    def get_name(model_name: PathLikeOrStr, checkpoint: PathLikeOrStr) -> str:
        """Return model name string."""
        return f"mmdet_{Path(model_name).stem}_{Path(checkpoint).stem}"


class MMDetDetector(MMDetDetectorMixin):
    """
    Document detector using MMDetection engine for layout analysis.

    This wrapper integrates MMDetection models for object detection tasks.
    It supports a variety of backbones and architectures compatible with MMDetection.

    Example:
        ```python
        from docnate.extern import MMDetDetector

        categories = {
            1: "text",
            2: "title",
            3: "table",
            4: "figure"
        }

        detector = MMDetDetector(
            model_name="configs/mask_rcnn/mask_rcnn_r50_fpn_1x_coco.py",
            checkpoint="weights/mask_rcnn_r50_fpn_1x_coco.pth",
            categories=categories
        )

        detection_results = detector.predict(bgr_image_np_array)
        ```
    """

    def __init__(
        self,
        model_name: PathLikeOrStr,
        checkpoint: PathLikeOrStr,
        categories: Mapping[int, TypeOrStr],
        device: Optional[Union[Literal["cpu", "cuda"], "torch.device"]] = None,
        filter_categories: Optional[Sequence[TypeOrStr]] = None,
    ) -> None:
        """
        Initialize the MMDetection detector.

        Args:
            model_name: Path to MMDetection model config (.py)
            checkpoint: Path to checkpoint (.pth)
            categories: Mapping of class IDs to class names
            device: Inference device ("cpu" or "cuda")
            filter_categories: Optional list of categories to filter out
        """
        if not mmdet_available():
            raise DependencyError("mmdet is not installed. Please install it with 'pip install mmdet'")
        if not pytorch_available():
            raise DependencyError("PyTorch is not installed. Please install it with 'pip install torch'")

        super().__init__(categories, filter_categories)

        self.model_name = Path(model_name)
        self.checkpoint = Path(checkpoint)
        self.device = get_torch_device(device)

        self.name = self.get_name(self.model_name, self.checkpoint)
        self.model_id = self.get_model_id()

        # Patch torch.load to allow loading legacy checkpoints
        old_load = torch.load

        def patched_load(*args, **kwargs):
            kwargs["weights_only"] = False
            return old_load(*args, **kwargs)

        checkpoint_runner.torch.load = patched_load

        # Load MMDetection model
        self.inferencer = DetInferencer(
            model=str(self.model_name),
            weights=str(self.checkpoint),
            device=self.device,
        )

    def predict(self, np_img: PixelValues) -> list[DetectionResult]:
        """
        Perform inference on an image using MMDetection.

        Args:
            np_img: Input image as numpy array (BGR format)

        Returns:
            A list of DetectionResult objects.
        """
        detection_results = mmdet_predict_image(np_img, self.inferencer)
        return self._map_category_names(detection_results)

    @classmethod
    def get_requirements(cls) -> list[Requirement]:
        """Return required dependencies."""
        return [get_pytorch_requirement(), get_mmdet_requirement()]

    def clone(self) -> MMDetDetector:
        """Clone the current detector instance."""
        return self.__class__(
            model_name=self.model_name,
            checkpoint=self.checkpoint,
            categories=self.categories.get_categories(),
            device=self.device,
            filter_categories=self.categories.filter_categories,
        )

    def clear_model(self) -> None:
        """Clear the inner model (for retraining or unloading)."""
        self.inferencer = None
