# -*- coding: utf-8 -*-
# File: identifier.py

# Copyright 2021 Dr. <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Generating and checking uuids
"""
import hashlib
import uuid

from .types import PathLikeOrStr

__all__ = ["is_uuid_like", "get_uuid_from_str", "get_uuid"]


def is_uuid_like(input_id: str) -> bool:
    """
    Check if the input string has a UUID3 string representation format.

    Example:
        ```python
        is_uuid_like("886313e1-3b8a-5372-9b90-0c9aee199e5d")
        ```

    Args:
        input_id: An input string.

    Returns:
        A boolean output.
    """
    try:
        uuid.UUID(str(input_id))
        return True
    except ValueError:
        return False


def get_uuid_from_str(input_id: str) -> str:
    """
    Return a UUID3 string representation generated from an input string.

    Args:
        input_id: Input string.

    Returns:
        UUID3 string representation.
    """
    return str(uuid.uuid3(uuid.NAMESPACE_DNS, input_id))


def get_uuid(*inputs: str) -> str:
    """
    Set a UUID generated by the concatenation of string inputs.

    Args:
        *inputs: String inputs.

    Returns:
        UUID3 string representation.
    """
    str_input = "".join(inputs)
    return get_uuid_from_str(str_input)


def get_md5_hash(path: PathLikeOrStr, buffer_size: int = 65536) -> str:
    """
    Calculate an MD5 hash for a given file.

    Args:
        path: Path to a file.
        buffer_size: Will calculate the hash in chunks.

    Returns:
        MD5 string.
    """

    hash_md5 = hashlib.md5()

    with open(path, "rb") as file:
        for chunk in iter(lambda: file.read(buffer_size), b""):
            hash_md5.update(chunk)

    return hash_md5.hexdigest()
