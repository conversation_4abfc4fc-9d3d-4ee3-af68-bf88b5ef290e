"""
Utilities for DOCX file handling and conversion
"""

import os
import platform
import tempfile
from pathlib import Path
from typing import Optional

from lazy_imports import try_import

from .error import DependencyError
from .file_utils import docx2pdf_available
from .logger import LoggingRecord, logger
from .types import PathLikeOrStr, FileConversionResult, ImageArray

import io
import numpy as np
from PIL import Image

with try_import() as docx2pdf_import_guard:
    from docx2pdf import convert as docx2pdf_convert

with try_import() as docx_import_guard:
    from docx import Document

with try_import() as reportlab_import_guard:
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch

__all__ = ["convert_docx_to_pdf", "convert_docx_to_pdf_bytes", "_convert_docx_to_images_memory"]


def _check_dependencies() -> tuple[bool, str]:
    """
    Check which conversion method is available.

    Returns:
        Tuple of (is_available, method_name)
    """
    # Check if we're on Windows and docx2pdf is available
    if platform.system() == "Windows" and docx2pdf_available():
        return True, "docx2pdf"

    # Check if python-docx and reportlab are available for cross-platform conversion
    try:
        import docx  # noqa: F401
        import reportlab  # noqa: F401
        return True, "reportlab"
    except ImportError:
        pass

    return False, "none"


def _convert_with_reportlab(docx_path: PathLikeOrStr, output_path: str) -> None:
    """
    Convert DOCX to PDF using python-docx and reportlab.

    Args:
        docx_path: Path to the input DOCX file
        output_path: Path for the output PDF file
    """
    # Read DOCX content
    doc = Document(docx_path)

    # Create PDF
    pdf_doc = SimpleDocTemplate(output_path, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []

    # Add content from DOCX
    for paragraph in doc.paragraphs:
        if paragraph.text.strip():
            # Determine style based on paragraph formatting
            if paragraph.style.name.startswith('Heading'):
                style = styles['Heading1']
            else:
                style = styles['Normal']

            # Add paragraph to PDF
            p = Paragraph(paragraph.text, style)
            story.append(p)
            story.append(Spacer(1, 0.2*inch))

    # Build PDF
    pdf_doc.build(story)


def convert_docx_to_pdf(docx_path: PathLikeOrStr, output_path: Optional[PathLikeOrStr] = None) -> str:
    """
    Converts a DOCX file to PDF format using the best available method.

    Args:
        docx_path: Path to the input DOCX file.
        output_path: Path for the output PDF file. If None, creates a temporary file.

    Returns:
        Path to the generated PDF file.

    Raises:
        DependencyError: If no conversion method is available.
        FileNotFoundError: If the input DOCX file does not exist.
        ValueError: If the input file is not a DOCX file.
    """
    # Check dependencies
    is_available, method = _check_dependencies()
    if not is_available:
        raise DependencyError(
            "No DOCX to PDF conversion method available. "
            "Please install either 'docx2pdf' (Windows) or 'python-docx' and 'reportlab' (cross-platform)"
        )

    if not os.path.isfile(docx_path):
        raise FileNotFoundError(f"DOCX file not found: {docx_path}")

    # Validate file extension
    if not str(docx_path).lower().endswith('.docx'):
        raise ValueError(f"Input file must be a DOCX file: {docx_path}")

    try:
        if output_path is None:
            # Create temporary PDF file
            temp_dir = tempfile.gettempdir()
            docx_name = Path(docx_path).stem
            output_path = os.path.join(temp_dir, f"{docx_name}_converted.pdf")

        logger.info(LoggingRecord(f"Converting DOCX to PDF using {method}: {docx_path} -> {output_path}"))

        # Convert DOCX to PDF using the available method
        if method == "docx2pdf":
            docx2pdf_convert(str(docx_path), str(output_path))
        elif method == "reportlab":
            _convert_with_reportlab(docx_path, str(output_path))

        if not os.path.isfile(output_path):
            raise RuntimeError(f"PDF conversion failed: output file not created at {output_path}")

        logger.info(LoggingRecord(f"Successfully converted DOCX to PDF: {output_path}"))
        return str(output_path)

    except Exception as e:
        logger.error(LoggingRecord(f"Failed to convert DOCX to PDF: {docx_path}. Error: {str(e)}"))
        raise RuntimeError(f"DOCX to PDF conversion failed: {str(e)}") from e


def convert_docx_to_pdf_bytes(docx_path: PathLikeOrStr) -> bytes:
    """
    Converts a DOCX file to PDF and returns the PDF content as bytes.

    Args:
        docx_path: Path to the input DOCX file.

    Returns:
        PDF content as bytes.

    Raises:
        DependencyError: If docx2pdf is not installed.
        FileNotFoundError: If the input DOCX file does not exist.
        ValueError: If the input file is not a DOCX file.
    """
    # Convert to temporary PDF file
    pdf_path = convert_docx_to_pdf(docx_path)
    
    try:
        # Read PDF content as bytes
        with open(pdf_path, 'rb') as pdf_file:
            pdf_bytes = pdf_file.read()
        
        # Clean up temporary file
        if pdf_path.endswith('_converted.pdf'):
            try:
                os.remove(pdf_path)
                logger.info(LoggingRecord(f"Cleaned up temporary PDF file: {pdf_path}"))
            except OSError as e:
                logger.warning(LoggingRecord(f"Failed to clean up temporary PDF file {pdf_path}: {str(e)}"))
        
        return pdf_bytes
        
    except Exception as e:
        # Clean up temporary file on error
        if pdf_path.endswith('_converted.pdf'):
            try:
                os.remove(pdf_path)
            except OSError:
                pass
        raise RuntimeError(f"Failed to read converted PDF: {str(e)}") from e


def _convert_docx_to_images_memory(file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
    """Convert DOCX to images - extract embedded images from memory"""
    try:
        # Load DOCX from bytes
        doc_stream = io.BytesIO(file_data)
        doc = Document(doc_stream)

        image_arrays = []

        # Extract images from document relationships
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                image_data = rel.target_part.blob
                image = Image.open(io.BytesIO(image_data))
                img_array = np.array(image)
                image_arrays.append(ImageArray(
                    data=img_array,
                    width=img_array.shape[1],
                    height=img_array.shape[0],
                    channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
                ))

        # If no images found, create a text-based image representation
        if not image_arrays:
            # For now, return empty - could implement text-to-image conversion later
            logger.debug(LoggingRecord(f"No images found in DOCX file {file_id}"))

        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )
    except Exception as e:
        return FileConversionResult(
            success=False,
            file_id=file_id,
            original_extension=file_extension,
            error_message=f"Error processing DOCX: {str(e)}"
        )
