# -*- coding: utf-8 -*-
# File: registry.py

# Copyright 2021 Dr<PERSON> <PERSON><PERSON>. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
MetricRegistry for registering and retrieving evaluation metrics
"""

import catalogue  # type: ignore

from .base import MetricBase

metric_registry = catalogue.create("docnate", "metrics", entry_points=True)


def get_metric(name: str) -> MetricBase:
    """
    Returns an instance of a metric with a given name.

    Args:
        name: A metric name

    Returns:
        A metric instance
    """
    return metric_registry.get(name)()
