# This workflow will install Python dependencies, run tests

name: Tests

on:
  push:
    branches:
      - "*"
  pull_request:
    branches: [ master ]

permissions:
  contents: read

jobs:
  tests:
    if: "contains(github.event.head_commit.message, '[force ci]') || github.ref == 'refs/heads/master' || github.event_name == 'pull_request'"
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        include:
          - { name: '3.9-basic-tf',
              python: '3.9',
              pip: '24.0',
              os: ubuntu-20.04,
              dll: tensorflow-cpu==2.11,
              detectron2: false,
              extras: ,
              test: test,
              test_cases: test-basic }
          - { name: '3.9-basic-pt',
              python: '3.9',
              pip: '24.0',
              os: ubuntu-20.04,
              dll: 'torch torchvision --extra-index-url https://download.pytorch.org/whl/cpu',
              detectron2: false,
              extras: ,
              test: test,
              test_cases: test-basic-pt }
          - { name: '3.9-tf',
              python: '3.9',
              pip: '24.0',
              os: ubuntu-20.04,
              dll: tensorflow-cpu==2.11,
              detectron2: false,
              extras: tf,
              test: test,
              test_cases: test-tf }
          - { name: '3.9-pt',
              python: '3.9',
              pip: '24.0',
              os: ubuntu-20.04,
              dll: 'torch torchvision --extra-index-url https://download.pytorch.org/whl/cpu',
              detectron2: true,
              extras: pt,
              test: test,
              test_cases: test-pt }
          - { name: '3.10-basic-tf',
              python: '3.10',
              pip: '24.0',
              os: ubuntu-22.04,
              dll: tensorflow-cpu==2.11,
              detectron2: false,
              extras: ,
              test: test,
              test_cases: test-basic }
          - { name: '3.10-basic-pt',
              python: '3.10',
              pip: '24.0',
              os: ubuntu-22.04,
              dll: 'torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu',
              detectron2: false,
              extras: ,
              test: test,
              test_cases: test-basic-pt }
          - { name: '3.10-tf',
              python: '3.10',
              pip: '24.0',
              os: ubuntu-22.04,
              dll: tensorflow-cpu==2.11,
              detectron2: false,
              extras: tf,
              test: test,
              test_cases: test-tf }
          - { name: '3.10-pt',
              python: '3.10',
              pip: '24.0',
              os: ubuntu-22.04,
              dll: 'torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu',
              detectron2: true,
              extras: pt,
              test: test,
              test_cases: test-pt }
          - { name: '3.11-pt',
              python: '3.11',
              pip: '24.0',
              os: ubuntu-22.04,
              dll: 'torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu',
              detectron2: true,
              extras: pt,
              test: test,
              test_cases: test-pt }
          #- { name: '3.12-pt',
          #    python: '3.12',
          #    pip: '24.0',
          #    os: ubuntu-22.04,
          #    dll: 'torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu',
          #    detectron2: true,
          #    extras: pt,
          #    test: test,
          #    test_cases: test-pt }

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python }}

      - name: Upgrade pip
        run: python3 -m pip install pip==${{ matrix.pip }} && 
             python3 -m pip install wheel

      #- name: Install setuptools
      #  if: matrix.python == '3.12'
      #  run: python3 -m pip install setuptools

      - name: Install libcap
        run: sudo apt-get install build-essential libcap-dev

      - name: Set Python version
        # See https://pre-commit.com/#github-actions-example
        run: echo "PY=$(python -VV | sha256sum | cut -d' ' -f1)" >> $GITHUB_ENV

      - name: Install tesseract and poppler
        run: sudo apt-get -y update &&
             sudo apt-get install -y tesseract-ocr tesseract-ocr-deu &&
             sudo apt-get install poppler-utils

      - name: Install DL library
        run: pip install ${{ matrix.dll }}

      - name: cd
        run: cd docnate

      - name: Install detectron2
        if: matrix.detectron2
        run: pip install --no-build-isolation detectron2@git+https://github.com/facebookresearch/detectron2.git

      - name: Test docnate package installation
        if: matrix.extras == ''
        run: pip install "."

      - name: Test docnate with extras package installation
        if: matrix.extras!= ''
        run:  pip install ".[${{ matrix.extras }}]"

      - name: Install test suite
        run: pip install ".[${{ matrix.test }}]"

      - name: Run tests
        run: make ${{ matrix.test_cases }}