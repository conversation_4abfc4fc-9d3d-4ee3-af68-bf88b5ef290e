name: Release

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  pypi-check:
    if: "!github.event.release.prerelease"
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.9
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
        architecture: x64

    - name: Install package
      run: |
           python3 -m pip install --upgrade pip
           pip install docnate
           python -c "import docnate; print(docnate.__version__)"