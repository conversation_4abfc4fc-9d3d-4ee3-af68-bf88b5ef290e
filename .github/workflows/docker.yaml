name: Publish Docker image

on:
  release:
    types: [published]

jobs:
  push_to_registry:
    name: Build and push Docker image to Docker Hub
    runs-on: docker_image_build_runner
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: docnate/docnate
          tags: |
            # branch event
            type=ref,event=branch
            # tag event
            type=ref,event=tag
            # pull request event
            # minimal
            type=semver,pattern={{major}}.{{minor}}

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: docker/pytorch-gpu/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
