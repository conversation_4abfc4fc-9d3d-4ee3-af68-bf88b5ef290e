TAG: tp_casc_rcnn_X_32xd4_50_FPN_GN_2FC
BACKBONE:
  BOTTLENECK: resnext_32xd4
  FREEZE_AFFINE: false
  FREEZE_AT: 0
  NORM: GN
  RESNET_NUM_BLOCKS:
    - 3
    - 4
    - 6
    - 3
  TF_PAD_MODE: false
CASCADE:
  BBOX_REG_WEIGHTS:
    - - 10.0
      - 10.0
      - 5.0
      - 5.0
    - - 20.0
      - 20.0
      - 10.0
      - 10.0
    - - 30.0
      - 30.0
      - 15.0
      - 15.0
  IOUS:
    - 0.5
    - 0.6
    - 0.7
DATA:
  TRAIN_NUM_WORKERS: 12
FPN:
  ANCHOR_STRIDES: !!python/tuple
    - 4
    - 8
    - 16
    - 32
    - 64
  CASCADE: true
  FRCNN_CONV_HEAD_DIM: 256
  FRCNN_FC_HEAD_DIM: 1024
  FRCNN_HEAD_FUNC: fastrcnn_2fc_head
  MRCNN_HEAD_FUNC: None
  NORM: GN
  NUM_CHANNEL: 256
  PROPOSAL_MODE: Level
FRCNN:
  BATCH_PER_IM: 512
  BBOX_REG_WEIGHTS:
    - 10.0
    - 10.0
    - 5.0
    - 5.0
  FG_RATIO: 0.25
  FG_THRESH: 0.5
MODE_MASK: false
MRCNN:
  ACCURATE_PASTE: true
  HEAD_DIM: 256
PREPROC:
  MAX_SIZE: 1333
  PIXEL_MEAN:
    - 238.234
    - 238.14
    - 238.145
  PIXEL_STD:
    - 7.961
    - 7.876
    - 7.81
  SHORT_EDGE_SIZE: 800
  TRAIN_SHORT_EDGE_SIZE:
    - 800
    - 1200
RPN:
  ANCHOR_RATIOS: !!python/tuple
    - 0.5
    - 1.0
    - 2.0
  ANCHOR_SIZES: !!python/tuple
    - 32
    - 64
    - 128
    - 256
    - 512
  ANCHOR_STRIDE: 16
  BATCH_PER_IM: 256
  CROWD_OVERLAP_THRESH: 9.99
  FG_RATIO: 0.5
  HEAD_DIM: 1024
  MIN_SIZE: 0
  NEGATIVE_ANCHOR_THRESH: 0.3
  POSITIVE_ANCHOR_THRESH: 0.7
  PROPOSAL_NMS_THRESH: 0.7
  PER_LEVEL_NMS_TOPK: 1000
  TRAIN_PER_LEVEL_NMS_TOPK: 2000
  TRAIN_PRE_NMS_TOPK: 12000
  PRE_NMS_TOPK: 6000
  TRAIN_POST_NMS_TOPK: 2000
  POST_NMS_TOPK: 1000
OUTPUT:
  FRCNN_NMS_THRESH: 0.001
  RESULTS_PER_IM: 100
  RESULT_SCORE_THRESH: 0.1
  NMS_THRESH_CLASS_AGNOSTIC: 0.001
TRAINER: replicated
TRAIN:
  LR_SCHEDULE: 4x
  EVAL_PERIOD: 50
  CHECKPOINT_PERIOD: 20
  WEIGHT_DECAY: 1e-4
  BASE_LR: 1e-2
  WARMUP: 1000
  WARMUP_INIT_LR: 1e-5
  STEPS_PER_EPOCH: 500
  STARTING_EPOCH: 1
  LOG_DIR: train_log/maskrcnn
