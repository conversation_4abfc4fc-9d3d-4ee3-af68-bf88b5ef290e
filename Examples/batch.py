from docnate.extern.yolov5detect import YoloV5Detector
from docnate.extern.paddleocr import Pa<PERSON>OcrDetector
from docnate.pipe import TextExtractionService, TextOrderService, PageParsingService
from docnate.extern.tessocr import TesseractOcrDetector
from docnate.pipe.doctectionpipe import DoctectionPipe
from docnate.pipe.layout import ImageLayoutService
from docnate.pipe.text import TextExtractionService
import docnate as dd
import matplotlib.pyplot as plt
from docnate.datapoint.view import Layout, IMAGE_DEFAULTS
import os
import pandas as pd
from docnate.utils.settings import Relationships
from docnate.pipe.common import IntersectionMatcher, FamilyCompound
from pathlib import Path
import cv2 # Added for image saving
import numpy as np # Added for image processing
from docnate.utils.settings import Relationships, LayoutType

dir_path="/home/<USER>/python ml/Equifax/card_dataset/batches"
batches = sorted([int(name) for name in os.listdir('/home/<USER>/python ml/Equifax/card_dataset/batches') if name.isdigit()])
model_name = "/home/<USER>/python ml/equifax-card_redaction/weights/best.pt"
print(batches)
@dd.object_types_registry.register("YOLOINVOICE")
class YOLOICARD(dd.ObjectTypes):
    """Additional YOLO labels for card elements"""

    CARD = "card"
    CARD_NUMBER = "card_number"
    NO_CARD = "no_card"


IMAGE_DEFAULTS.IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in YOLOICARD})

yolo_categories = {
        1: YOLOICARD.CARD,
        2: YOLOICARD.CARD_NUMBER,
        3: YOLOICARD.NO_CARD
}


dd.ModelCatalog.register(model_name, dd.ModelProfile(
    name=model_name,
    description="YOLO model for layout analysis",
    tp_model=False,
    size=[],
    categories=yolo_categories,
    model_wrapper="YoloV5Detector"
))

for batch in batches:
    print("Running for batch: ",batch)

    if batch % 2 != 0 :

        # Define model paths and categories
        path = f"{dir_path}/{batch}"

        # Extract base file name
        base_file_name = Path(path).stem



        yolo_weights_path = dd.ModelCatalog.get_full_path_weights(model_name)
        categories = dd.ModelCatalog.get_profile(model_name).categories
        print("Categories: ",categories)
        yolo_detector = YoloV5Detector(path_weights=model_name,
                                categories=categories,
                                conf_threshold=0.1,
                                iou_threshold=0.1)

        layout_service = ImageLayoutService(yolo_detector, to_image=True, crop_image=True)

        categories_names_from_model = [layout_item for layout_item in list(categories.values())]
        # Define categories for PaddleOCR
        paddle_categories = {
            1: dd.LayoutType.WORD,
        }

        text_detector = PaddleOcrDetector(
            categories=paddle_categories,
            lang="en",
            use_angle_cls=True,
            use_gpu=False
        )
        text = TextExtractionService(text_detector)
        matcher = IntersectionMatcher(matching_rule='ioa', threshold=0.3, max_parent_only=True)
        family_compound = FamilyCompound(relationship_key=Relationships.CHILD,
                                        parent_categories=categories_names_from_model,
                                        child_categories=[dd.LayoutType.WORD])
        map_comp = dd.MatchingService(family_compounds=[family_compound], matcher=matcher)
        text_order_comp = dd.TextOrderService(text_container=dd.LayoutType.WORD,
                                            text_block_categories= categories_names_from_model,
                                            floating_text_block_categories=categories_names_from_model,
                                            include_residual_text_container=True)

        page_parsing = dd.PageParsingService(text_container=dd.LayoutType.WORD,
                                                floating_text_block_categories=categories_names_from_model,
                                                include_residual_text_container=False)

        pipe_comp_list=[layout_service,text,map_comp,text_order_comp]
        analyzer = DoctectionPipe(pipeline_component_list=pipe_comp_list,page_parsing_service=page_parsing)

        df=analyzer.analyze(path=path, extract_images_from_docx=True)
        df.reset_state()
        print("pdf:",path)

        input_images_dir = path # Define input_images_dir here
        output_dir = "annotated_output"
        os.makedirs(output_dir, exist_ok=True)

        def show_and_save_combined_annotations(original_image_path, cnt, layout_annotations_data, word_annotations_data, output_dir):
            """
            Loads the original image, annotates it with combined layout and word annotations,
            and saves the original and annotated images side-by-side.
            layout_annotations_data is a list of (category, bbox_list, score) tuples for layouts.
            word_annotations_data is a list of (score, bbox_list, text) tuples for words.
            """
            print(f"Loading original image for annotation: {original_image_path}")
            original_img_bgr = cv2.imread(original_image_path)
            if original_img_bgr is None:
                print(f"  Could not read original image: {original_image_path}. Skipping annotation.")
                return None

            base_file_name = Path(original_image_path).stem

            # Create a copy of the original image for annotations
            annotated_img_bgr = original_img_bgr.copy()

            # Draw layout annotations (green rectangles) with only label
            for category, bbox, score in layout_annotations_data:
                x1, y1, x2, y2 = [int(coord) for coord in bbox]
                cv2.rectangle(annotated_img_bgr, (x1, y1), (x2, y2), (0, 255, 0), 2) # Green rectangle

                label = f"{category}"
                cv2.putText(annotated_img_bgr, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # Draw word annotations (blue rectangles) with only text
            for score, bbox, text in word_annotations_data:
                x1, y1, x2, y2 = [int(coord) for coord in bbox]
                cv2.rectangle(annotated_img_bgr, (x1, y1), (x2, y2), (255, 0, 0), 1) # Blue rectangle for words

                label = f"{dd.LayoutType.WORD.value}" # Display "WORD" as the label for word annotations
                cv2.putText(annotated_img_bgr, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

            # Combine original and annotated images side-by-side
            h, w, _ = original_img_bgr.shape
            combined_image = np.zeros((h, w * 2, 3), dtype=np.uint8)
            combined_image[:, :w] = original_img_bgr
            combined_image[:, w:] = annotated_img_bgr

            # Save the combined image
            output_filename = f"{base_file_name}_page_{cnt}_combined.png"
            output_path = os.path.join(output_dir, output_filename)
            cv2.imwrite(output_path, combined_image)
            print(f"  Saved combined original and annotated image to {output_path}")
            
            return output_path # Return the path of the saved image

        df.reset_state()
        cnt = 0

        for dp in df: # 'df' is the Dataflow returned by analyzer.analyze()
            cnt=cnt+1
            print(f"--- Processing Page {dp.page_number} ---")
            
            # Construct the original image path
            original_image_path = os.path.join(input_images_dir, dp.file_name)

            # Collect layout annotations
            print("  Detected Layouts:")
            page_layout_annotations_data = []
            for layout in dp.layouts:
                category = layout.category_name.value
                bbox = layout.bounding_box.to_list(mode='xyxy')
                score = layout.score
                print(f"    ({category}, {bbox}, {score})")
                page_layout_annotations_data.append((category, bbox, score))
            print("-" * 20)

            # Collect word annotations
            print("  Individual Words and their Bounding Boxes:")
            page_word_annotations_data = []
            word_annotations = dp.get_annotation(category_names=LayoutType.WORD)
            if word_annotations:
                for word_ann in word_annotations:
                    if word_ann.characters and word_ann.bounding_box:
                        score = word_ann.score if word_ann.score is not None else 1.0
                        bbox = word_ann.bounding_box.to_list(mode="xyxy")
                        text = word_ann.characters
                        print(f"    OCRResult : ({score:.2f}, {bbox}, '{text}')")
                        page_word_annotations_data.append((score, bbox, text))
            else:
                print("  No word-level annotations found for this page.")
            print("-" * 20)

            # Annotate and save the original image
            show_and_save_combined_annotations(original_image_path, cnt, page_layout_annotations_data, page_word_annotations_data, output_dir)
