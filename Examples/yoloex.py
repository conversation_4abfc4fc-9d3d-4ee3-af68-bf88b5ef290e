from docnate.extern.yolopred import YoloDetector
from docnate.pipe.layout import ImageLayoutService
from docnate.pipe.text import TextExtractionService
from docnate.extern.texextracter import PdfPlumberTextDetector
import docnate as dd
import matplotlib.pyplot as plt
from docnate.datapoint.view import IMAGE_ANNOTATION_TO_LAYOUTS, Layout


# Define model paths and categories
model_name = "yolo/yolo10x/yolov10x_best.pt"
path = "Examples/data/pdf/sample.pdf"

@dd.object_types_registry.register("YOLOTYPE")
class YOLOEXTENSION(dd.ObjectTypes):
    """Additional YOLO labels not registered yet"""

    YOLO_PICTURE =  "picture",

IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in YOLOEXTENSION})

yolo_categories = {
        1: dd.LayoutType.CAPTION,
        2: dd.LayoutType.FOOTNOTE,
        3: dd.LayoutType.FORMULA,
        4: dd.LayoutType.LIST_ITEM,
        5: dd.LayoutType.PAGE_FOOTER,
        6: dd.LayoutType.PAGE_HEADER,
        7: YOLOEXTENSION.YOLO_PICTURE,
        8: dd.LayoutType.SECTION_HEADER,
        9: dd.LayoutType.TABLE,
        10: dd.LayoutType.TEXT,
        11: dd.LayoutType.TITLE,
}


dd.ModelCatalog.register(model_name, dd.ModelProfile(
    name=model_name,
    description="YOLOv10 model for layout analysis",
    tp_model=False,
    size=[],
    categories=yolo_categories,
    model_wrapper="YoloDetector"
))

yolo_weights_path = dd.ModelCatalog.get_full_path_weights(model_name)
categories = dd.ModelCatalog.get_profile(model_name).categories
print("Categories: ",categories)

yolo_detector = YoloDetector(model_weights=yolo_weights_path, categories=categories)

layout_service = ImageLayoutService(yolo_detector)

text_detector=PdfPlumberTextDetector()
text=TextExtractionService(text_detector)

categories_names_from_model = [layout_item for layout_item in list(categories.values())]


# Initialize other components like the matching service and text order service
map_comp = dd.MatchingService(parent_categories=categories_names_from_model,    
                              child_categories=[dd.LayoutType.WORD], 
                              matching_rule='ioa', threshold=0.3,
                              max_parent_only=True)
text_order_comp = dd.TextOrderService(text_container=dd.LayoutType.WORD,
                                     text_block_categories=categories_names_from_model,
                                     floating_text_block_categories=categories_names_from_model,
                                     include_residual_text_container=True)

page_parsing = dd.PageParsingService(text_container=dd.LayoutType.WORD,
                                         floating_text_block_categories=categories_names_from_model,
                                         include_residual_text_container=False)


# Add CamelotTableDetector to the pipeline
pipe_comp_list = [layout_service]#,text,map_comp,text_order_comp]
pipe = dd.DoctectionPipe(pipeline_component_list=pipe_comp_list)#,page_parsing_service=page_parsing)

df = pipe.analyze(path=path)
df.reset_state()
# for dp in df:
#     print(dp.layouts)



# FUNCTION TO SHOW ANNOTATED AREAS
def show_image(page) :
    image = page.viz()
    if image is not None:
        plt.figure(figsize=(25, 17))
        plt.axis('off')
        plt.imshow(image)
        plt.show()  # Ensure the plot window opens
    else:
        print("No image generated.")


df.reset_state()
cnt = 0
for dp in df :
    # cnt=cnt+1
    # print(f"PAGE : {cnt}")
    show_image(dp)
    # for layout in dp.layouts:
    #     if len(layout.text)>0:
    #         print(f"{layout.category_name.upper()}: {layout.text}")
    #         print()
    # if dp.tables :
    #     print(type(dp.tables[0]))
    #     print("Table found")
    # print()
    # print()
 