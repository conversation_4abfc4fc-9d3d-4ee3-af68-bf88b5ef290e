from docnate.extern.yolov5detect import <PERSON><PERSON><PERSON>5<PERSON>etector
from docnate.extern.paddleocr import <PERSON><PERSON>OcrDetector
from docnate.pipe import TextExtractionService, TextOrderService, PageParsingService
from docnate.extern.tessocr import TesseractOcrDetector
from docnate.pipe.doctectionpipe import DoctectionPipe
from docnate.pipe.layout import ImageLayoutService
from docnate.pipe.text import TextExtractionService
import docnate as dd
import matplotlib.pyplot as plt
from docnate.datapoint.view import Layout, IMAGE_DEFAULTS
import os
import pandas as pd
from docnate.utils.settings import Relationships
from docnate.pipe.common import IntersectionMatcher, FamilyCompound
from pathlib import Path
import cv2 # Added for image saving
import numpy as np # Added for image processing
import io
from docx import Document
from PIL import Image
import shutil

# Define model paths and categories
model_name = "/home/<USER>/python ml/equifax-card_redaction/weights/best.pt"
path = "/home/<USER>/Downloads/finaldata 1/finaldata/2.docx"

def extract_images_from_docx_file(docx_path: Path, output_dir: Path) -> Path:
    """
    Extracts images from a DOCX file, converts them to RGB, and saves them to an output directory.
    Returns the path to the output directory.
    """
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(docx_path, "rb") as f:
        doc_stream = io.BytesIO(f.read())
    doc = Document(doc_stream)
    
    image_count = 0
    for rel in doc.part.rels.values():
        if "image" in rel.target_ref:
            image_data = rel.target_part.blob
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if not already
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            
            image_path = output_dir / f"image_{image_count}.png"
            image.save(image_path)
            image_count += 1
    
    if image_count == 0:
        print(f"No images found in DOCX file {docx_path}. Proceeding with docnate on the empty directory.")
    
    return output_dir


@dd.object_types_registry.register("YOLOINVOICE")
class YOLOICARD(dd.ObjectTypes):
    """Additional YOLO labels for card elements"""

    CARD = "card"
    CARD_NUMBER = "card_number"
    NO_CARD = "no_card"


IMAGE_DEFAULTS.IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in YOLOICARD})

yolo_categories = {
        1: YOLOICARD.CARD,
        2: YOLOICARD.CARD_NUMBER,
        3: YOLOICARD.NO_CARD
}


dd.ModelCatalog.register(model_name, dd.ModelProfile(
    name=model_name,
    description="YOLO model for layout analysis",
    tp_model=False,
    size=[],
    categories=yolo_categories,
    model_wrapper="YoloV5Detector"
))

yolo_weights_path = dd.ModelCatalog.get_full_path_weights(model_name)
categories = dd.ModelCatalog.get_profile(model_name).categories
print("Categories: ",categories)
yolo_detector = YoloV5Detector(path_weights=model_name,
                           categories=categories,
                           conf_threshold=0.90,
                           iou_threshold=0.90)

layout_service = ImageLayoutService(yolo_detector, to_image=True, crop_image=True)

# # Define categories for PaddleOCR
# paddle_categories = {
#     1: dd.LayoutType.WORD,
# }

# text_detector = PaddleOcrDetector(
#     categories=paddle_categories,
#     lang="en",
#     use_angle_cls=True,
#     use_gpu=False
# )
# text_extraction_service = TextExtractionService(text_detector)

path_weights_tl = dd.ModelDownloadManager.maybe_download_weights_and_configs("doctr/db_resnet50/pt/db_resnet50-ac60cadc.pt")

categorie = dd.ModelCatalog.get_profile("doctr/db_resnet50/pt/db_resnet50-ac60cadc.pt").categories
det = dd.DoctrTextlineDetector("db_resnet50",path_weights_tl,categorie,"cpu", absolute_coords=True)

doctrdet = ImageLayoutService(det, to_image=True, crop_image=True)

path_weights_tr = dd.ModelDownloadManager.maybe_download_weights_and_configs("doctr/crnn_vgg16_bn/pt/crnn_vgg16_bn-9762b0b0.pt")
rec = dd.DoctrTextRecognizer("crnn_vgg16_bn", path_weights_tr, "cpu")
text_extraction_service = TextExtractionService(rec, extract_from_roi=dd.LayoutType.WORD)


categories_names_from_model = [layout_item for layout_item in list(categories.values())]

# Initialize other components like the matching service and text order service
matcher = IntersectionMatcher(matching_rule='ioa', threshold=0.3, max_parent_only=True)
family_compound = FamilyCompound(relationship_key=Relationships.CHILD,
                                 parent_categories=categories_names_from_model,
                                 child_categories=[dd.LayoutType.WORD])
map_comp = dd.MatchingService(family_compounds=[family_compound], matcher=matcher)
text_order_comp = dd.TextOrderService(text_container=dd.LayoutType.WORD,
                                     text_block_categories= categories_names_from_model,
                                     floating_text_block_categories=categories_names_from_model,
                                     include_residual_text_container=True)

page_parsing = dd.PageParsingService(text_container=dd.LayoutType.WORD,
                                         floating_text_block_categories=categories_names_from_model,
                                         include_residual_text_container=False)

pipe_comp_list=[layout_service, doctrdet, text_extraction_service,map_comp,text_order_comp]
analyzer = DoctectionPipe(pipeline_component_list=pipe_comp_list,page_parsing_service=page_parsing)

# Create a temporary directory for extracted images
temp_image_dir = Path("temp_docx_images")
extracted_images_path = extract_images_from_docx_file(Path(path), temp_image_dir)

df=analyzer.analyze(path=extracted_images_path)
df.reset_state()
print("Processing images from:", extracted_images_path)
def show_image(page, output_dir="output_images") :
    img = page.viz(show_layouts=True, show_words=True) # Explicitly show layouts and words
    if img is not None:
        # Ensure the output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Construct output file path
        output_file = os.path.join(output_dir, f"annotated_{page.file_name}")
        
        # Convert image to BGR for OpenCV saving
        img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
        
        # Save the image
        cv2.imwrite(output_file, img_bgr)
        print(f"Annotated image saved to: {output_file}")
        
        # Optionally display the image (can be commented out if only saving is desired)
        plt.figure(figsize=(25, 17))
        plt.axis('off')
        plt.imshow(img)
        plt.show()
    else:
        print("No image generated.")


df.reset_state()
cnt = 0
for dp in df: # 'df' is the Dataflow returned by analyzer.analyze()
    cnt=cnt+1
    print(f"--- Processing Page {dp.page_number} ---")
    
    # Construct the original image path
    # original_image_path = os.path.join(input_images_dir, dp.file_name)
 
    # Collect layout annotations
    print("  Detected Layouts:")
    page_layout_annotations_data = []
    for layout in dp.layouts:
        category = layout.category_name.value
        bbox = layout.bounding_box.to_list(mode='xyxy')
        score = layout.score
        print(f"    ({category}, {bbox}, {score})")
        page_layout_annotations_data.append((category, bbox, score))
    print("-" * 20)
 
    # Collect word annotations
    print("  Individual Words and their Bounding Boxes:")
    page_word_annotations_data = []
    word_annotations = dp.get_annotation(category_names=dd.LayoutType.WORD)
    if word_annotations:
        for word_ann in word_annotations:
            if word_ann.characters and word_ann.bounding_box:
                score = word_ann.score if word_ann.score is not None else 1.0
                bbox = word_ann.bounding_box.to_list(mode="xyxy")
                text = word_ann.characters
                print(f"    OCRResult : ({score:.2f}, {bbox}, '{text}')")
                page_word_annotations_data.append((score, bbox, text))
    else:
        print("  No word-level annotations found for this page.")
    print("-" * 20)
