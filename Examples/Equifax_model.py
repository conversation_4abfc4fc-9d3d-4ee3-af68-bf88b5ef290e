from docnate.extern.yolov5detect import YoloV5Detector
from docnate.extern.paddleocr import PaddleOcrDetector
from docnate.pipe import TextExtractionService, TextOrderService, PageParsingService
from docnate.extern.tessocr import TesseractOcrDetector
from docnate.pipe.doctectionpipe import <PERSON>tec<PERSON><PERSON>ipe
from docnate.pipe.layout import ImageLayoutService
from docnate.pipe.text import TextExtractionService
import docnate as dd
import matplotlib.pyplot as plt
from docnate.datapoint.view import Layout, IMAGE_DEFAULTS
import os
import pandas as pd
from docnate.utils.settings import Relationships
from docnate.pipe.common import IntersectionMatcher, FamilyCompound
from pathlib import Path
import cv2 # Added for image saving
import numpy as np # Added for image processing

# Define model paths and categories
model_name = "/home/<USER>/python ml/equifax-card_redaction/weights/best.pt"
path = "/home/<USER>/python ml/Card.docx"


@dd.object_types_registry.register("YOLOINVOICE")
class YOLOICARD(dd.ObjectTypes):
    """Additional YOLO labels for card elements"""

    CARD = "card"
    CARD_NUMBER = "card_number"
    NO_CARD = "no_card"


IMAGE_DEFAULTS.IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in YOLOICARD})

yolo_categories = {
        1: YOLOICARD.CARD,
        2: YOLOICARD.CARD_NUMBER,
        3: YOLOICARD.NO_CARD
}


dd.ModelCatalog.register(model_name, dd.ModelProfile(
    name=model_name,
    description="YOLO model for layout analysis",
    tp_model=False,
    size=[],
    categories=yolo_categories,
    model_wrapper="YoloV5Detector"
))

yolo_weights_path = dd.ModelCatalog.get_full_path_weights(model_name)
categories = dd.ModelCatalog.get_profile(model_name).categories
print("Categories: ",categories)
yolo_detector = YoloV5Detector(path_weights=model_name,
                           categories=categories,
                           conf_threshold=0.1,
                           iou_threshold=0.1)

layout_service = ImageLayoutService(yolo_detector, to_image=True, crop_image=True)

categories_names_from_model = [layout_item for layout_item in list(categories.values())]
matcher = IntersectionMatcher(matching_rule='ioa', threshold=0.3, max_parent_only=True)
family_compound = FamilyCompound(relationship_key=Relationships.CHILD,
                                 parent_categories=categories_names_from_model,
                                 child_categories=[dd.LayoutType.WORD])
map_comp = dd.MatchingService(family_compounds=[family_compound], matcher=matcher)
text_order_comp = dd.TextOrderService(text_container=dd.LayoutType.WORD,
                                     text_block_categories= categories_names_from_model,
                                     floating_text_block_categories=categories_names_from_model,
                                     include_residual_text_container=True)

page_parsing = dd.PageParsingService(text_container=dd.LayoutType.WORD,
                                         floating_text_block_categories=categories_names_from_model,
                                         include_residual_text_container=False)

pipe_comp_list=[layout_service,map_comp,text_order_comp]
analyzer = DoctectionPipe(pipeline_component_list=pipe_comp_list,page_parsing_service=page_parsing)

df=analyzer.analyze(path=path)
df.reset_state()
print("pdf:",path)
def show_image(page):
    """
    Display the annotated page using OpenCV.
    """
    # Generate annotated image
    img = page.viz(show_layouts=True, show_words=True)
    
    if img is None:
        print("No image generated for this page.")
        return
    
    # Convert RGB (from docnate) to BGR for OpenCV
    img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    
    window_name = f"Page- Annotated"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)  # Make window resizable
    cv2.imshow(window_name, img_bgr)
    
    # Wait until a key is pressed
    cv2.waitKey(0)
    cv2.destroyAllWindows()



df.reset_state()
cnt = 0
for dp in df :
    cnt=cnt+1
    print(f"PAGE : {cnt}")
    show_image(dp)
    for layout in dp.layouts:
        if len(layout.text)>0:
            print(f"{layout.category_name.upper()}: {layout.text}")
            print()
    if dp.tables :
        print(type(dp.tables[0]))
        print("Table found")
    print()
