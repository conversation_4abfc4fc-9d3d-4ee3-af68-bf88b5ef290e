import logging
import os
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path

# Define logger configuration directly
LOG_DIR = "Services/equifax_service/logs"
LOG_FILE = "app.log"
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(message)s"

class AppLogger:
    """Application logger with daily rotating file handler"""
    
    def __init__(self):
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with daily rotating file handler"""
        # Create logs directory if it doesn't exist
        log_dir = Path(LOG_DIR)
        log_dir.mkdir(exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger("equifax_app")
        self.logger.setLevel(getattr(logging, LOG_LEVEL))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create daily rotating file handler
        log_file_path = log_dir / LOG_FILE
        handler = TimedRotatingFileHandler(
            filename=str(log_file_path),
            when='midnight',
            interval=1,
            backupCount=0,
            encoding='utf-8'
        )

        
        # Set formatter
        formatter = logging.Formatter(LOG_FORMAT)
        handler.setFormatter(formatter)
        
        # Add handler to logger
        self.logger.addHandler(handler)
        
        # Prevent propagation to root logger
        self.logger.propagate = False
    
    def log_api_call(self, filename: str, file_type: str, is_card: str, 
                     status: str, api: str, page_no: int = None,error: str = None):
        """Log API call in the format expected by the system"""
        timestamp = datetime.now().strftime("%d-%m-%Y %H:%M:%S")
        
        if page_no is not None:
            filename_log = f"{filename}_page{page_no}"
        else:
            filename_log = filename
            
        record = (f"FILENAME :{filename_log} FILETYPE :{file_type} "
                 f"ISCARD:{is_card} TIMESTAMP:{timestamp} "
                 f"STATUS:{status} API:{api}")
        
        if error:
            record += f" Error: {error}"

        self.logger.info(record)
    
    def log_error(self, message: str, exception: Exception = None):
        """Log error message"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}")
        else:
            self.logger.error(message)
    
    def log_info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def log_debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def log_warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)


# Global logger instance
app_logger = AppLogger()
