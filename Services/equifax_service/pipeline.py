from docnate.extern.yolov5detect import <PERSON><PERSON><PERSON>5Detector
from docnate.extern.paddleocr import <PERSON><PERSON>OcrDetector
from docnate.pipe import TextExtractionService, TextOrderService, PageParsingService, MatchingService
from docnate.extern.tessocr import TesseractOcrDetector
from docnate.pipe.doctectionpipe import DoctectionPipe
from docnate.pipe.layout import ImageLayoutService
import docnate as dd
from docnate.datapoint.view import Layout, IMAGE_DEFAULTS
from docnate.utils.settings import Relationships
from docnate.pipe.common import IntersectionMatcher, FamilyCompound
from docnate.utils.settings import TypeOrStr

def docnate_pipeline(model_name: TypeOrStr):
    # Define model paths and categories
    model_name = model_name
    # path = "/home/<USER>/python ml/Card.docx" # This will be handled by the upload endpoint

    try:
        @dd.object_types_registry.register("YOLOINVOICE")
        class YOLOICARD(dd.ObjectTypes):
            """Additional YOLO labels for card elements"""

            CARD = "card"
            CARD_NUMBER = "card_number"
            NO_CARD = "no_card"


        IMAGE_DEFAULTS.IMAGE_ANNOTATION_TO_LAYOUTS.update({i: Layout for i in YOLOICARD})

        yolo_categories = {
                1: YOLOICARD.CARD,
                2: YOLOICARD.CARD_NUMBER,
                3: YOLOICARD.NO_CARD
        }

    
        dd.ModelCatalog.register(model_name, dd.ModelProfile(
            name=model_name,
            description="YOLO model for layout analysis",
            tp_model=False,
            size=[],
            categories=yolo_categories,
            model_wrapper="YoloV5Detector"
        ))

    finally:
        categories = dd.ModelCatalog.get_profile(model_name).categories
        print("Categories: ",categories)
        yolo_detector = YoloV5Detector(path_weights=model_name,
                                categories=categories,
                                conf_threshold=0.1,
                                iou_threshold=0.1)

        layout_service = ImageLayoutService(yolo_detector, to_image=True, crop_image=True)

        categories_names_from_model = [layout_item for layout_item in list(categories.values())]
        matcher = IntersectionMatcher(matching_rule='ioa', threshold=0.3, max_parent_only=True)
        family_compound = FamilyCompound(relationship_key=Relationships.CHILD,
                                        parent_categories=categories_names_from_model,
                                        child_categories=[dd.LayoutType.WORD])
        map_comp = MatchingService(family_compounds=[family_compound], matcher=matcher)
        # Define categories for PaddleOCR
        paddle_categories = {
            1: dd.LayoutType.WORD,
        }

        text_detector = PaddleOcrDetector(
            categories=paddle_categories,
            lang="en",
            use_angle_cls=True,
            use_gpu=False # Set to True if GPU is available and configured
        )
        text_extraction_service = TextExtractionService(text_detector)

        text_order_comp =TextOrderService(text_container=dd.LayoutType.WORD,
                                            text_block_categories= categories_names_from_model,
                                            floating_text_block_categories=categories_names_from_model,
                                            include_residual_text_container=True)

        page_parsing = PageParsingService(text_container=dd.LayoutType.WORD,
                                                floating_text_block_categories=categories_names_from_model,
                                                include_residual_text_container=False)

        pipe_comp_list=[layout_service, text_extraction_service, map_comp, text_order_comp]
        analyzer = DoctectionPipe(pipeline_component_list=pipe_comp_list,page_parsing_service=page_parsing)
        return analyzer
