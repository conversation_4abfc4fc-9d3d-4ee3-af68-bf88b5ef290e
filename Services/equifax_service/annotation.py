from pathlib import Path
import cv2 # Added for image saving
import numpy as np # Added for image processing
import base64 # Added for base64 encoding of image bytes
from PIL import Image
import io
import re
from docx import Document # Added for DOCX processing
from deepdoctection.utils.settings import LayoutType
from docnate.datapoint.box import iou # Import iou function
import numpy as np # Ensure numpy is imported for iou function
from pdf2image import convert_from_bytes # For PDF processing
from pillow_heif import register_heif_opener # For HEIC support
from fastapi import UploadFile

register_heif_opener() # Register HEIF opener for Pillow

def _load_image_data(image_source: Path | bytes, file_type: str) -> np.ndarray | None:
    """
    Loads an image from the given path or BytesIO object, handling different file types.
    Returns a NumPy array (RGB) or None if loading fails.
    """
    data_to_process = None
    if isinstance(image_source, Path):
        if not image_source.is_file():
            return None
        data_to_process = image_source.read_bytes()
    elif isinstance(image_source, bytes):
        data_to_process = image_source
    else:
        return None # Invalid image_source type

    try:
        if file_type.lower() == 'pdf':
            # Convert PDF to image, take the first page
            images = convert_from_bytes(data_to_process, first_page=1, last_page=1, dpi=300)
            if images:
                return np.array(images[0])
            else:
                return None
        elif file_type.lower() == 'heic':
            # Pillow with pillow_heif opener handles HEIC
            with Image.open(io.BytesIO(data_to_process)) as img:
                return np.array(img)
        elif file_type.lower() == 'docx':
            # Load DOCX from bytes
            doc_stream = io.BytesIO(data_to_process)
            doc = Document(doc_stream)

            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_data = rel.target_part.blob
                    image = Image.open(io.BytesIO(image_data))
                    return np.array(image) # Return the first image found
            return None # No images found in DOCX
        else:
            # For other image types, use Pillow directly
            with Image.open(io.BytesIO(data_to_process)) as img:
                return np.array(img)
    except Exception as e:
        print(f"Error loading image from source of type {file_type}: {e}")
        return None

async def get_annotated_image_response_data(page, original_filename, original_file_type, original_image_path: Path = None, file_data: UploadFile = None):
    """
    Generates an annotated image from a docnate page object or an original image file,
    converts it to PNG bytes, base64 encodes it, and returns a dictionary
    for the API response.
    """
    img_array = None
    if file_data:
        file_bytes = await file_data.read()
        img_array = _load_image_data(file_bytes, original_file_type)
    elif original_image_path:
        img_array = _load_image_data(original_image_path, original_file_type)
    
    if img_array is None:
        # Fallback to page.image if original_image_path is not provided or file not found
        if isinstance(page.image, np.ndarray):
            img_array = page.image
        elif isinstance(page.image, Image.Image):
            img_array = np.array(page.image)
        elif hasattr(page.image, 'get_array'): # Assume it's a docnate.datapoint.image.Image object
            img_array = page.image.get_array()
    
    if img_array is None:
        print(f"No raw image array found for page in {original_filename}.")
        return None

    # Convert RGB to BGR for OpenCV for drawing
    annotated_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

    # Define colors for annotations (BGR format)
    COLOR_MAP = {
        "card": (255, 0, 0),       # Blue
        "card_number": (0, 255, 0), # Green
        "no_card": (0, 0, 255),     # Red
    }

    for layout in page.layouts:
        category = layout.category_name.value
        
        # Only draw annotations for specified categories
        if category in COLOR_MAP:
            bbox = layout.bounding_box.to_list(mode='xyxy') # [x1, y1, x2, y2]
            score = layout.score

            x1, y1, x2, y2 = map(int, bbox)
            color = COLOR_MAP.get(category) # Get color for the category
            
            # Draw rectangle
            cv2.rectangle(annotated_img, (x1, y1), (x2, y2), color, 2) # 2 is thickness

            # Add text label
            label = f"{category}: {score:.2f}" if score else category
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            font_thickness = 2
            text_size = cv2.getTextSize(label, font, font_scale, font_thickness)[0]
            
            # Position text above the bounding box
            text_x = x1
            text_y = y1 - 10 if y1 - 10 > text_size[1] else y1 + text_size[1] + 10 # Avoid going off-image top

            cv2.putText(annotated_img, label, (text_x, text_y), font, font_scale, color, font_thickness, cv2.LINE_AA)

    # Convert annotated image (BGR numpy array) to RGB for PIL, then to bytes
    annotated_img_rgb = cv2.cvtColor(annotated_img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(annotated_img_rgb)
    img_byte_arr = io.BytesIO()
    pil_img.save(img_byte_arr, format="PNG")
    img_bytes = img_byte_arr.getvalue()

    return {
        "Image_str": base64.b64encode(img_bytes).decode('utf-8'),
        "filename": original_filename,
        "file_type": original_file_type
    }


def is_card_number(text: str) -> bool:
    """Check if text looks like a card number"""
    if '/' in text:  # quick filter for expiry dates
        return False
    return text.isdigit() and len(text) > 3


async def get_masked_image_response_data(page, original_filename, original_file_type, original_image_path: Path = None, file_data: UploadFile=None):
    """
    Generates a masked image from a DeepDoctection page object or an original image file,
    converts it to PNG bytes, base64 encodes it, and returns a dictionary
    for the API response. This function applies masking to both model-detected
    card numbers and OCR-detected potential card numbers.
    """
    img_array = None
    if file_data:
        file_bytes = await file_data.read()
        img_array = _load_image_data(io.BytesIO(file_bytes), original_file_type)
    elif original_image_path:
        img_array = _load_image_data(original_image_path, original_file_type)
    
    if img_array is None:
        # Fallback to page.image if original_image_path is not provided or file not found
        if isinstance(page.image, np.ndarray):
            img_array = page.image
        elif isinstance(page.image, Image.Image):
            img_array = np.array(page.image)
        elif hasattr(page.image, 'get_array'): # Assume it's a docnate.datapoint.image.Image object
            img_array = page.image.get_array()
    
    if img_array is None:
        print(f"No raw image array found for page in {original_filename}.")
        return None

    # Convert RGB to BGR for OpenCV for drawing
    masked_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

    # Ensure correct dtype for OpenCV operations
    # if img_array.dtype != np.uint8:
    #     img_array = (img_array * 255).astype(np.uint8)

    # Define color for masking (red in BGR to match OCR masking)
    MASK_COLOR = (0, 0, 255) # Red in BGR
    
    masked_cards_details = [] # To store details of each masked card

    card_layouts = [layout for layout in page.layouts if layout.category_name.value == "card"]
    card_number_layouts = [layout for layout in page.layouts if layout.category_name.value == "card_number"]

    # Keep track of card layouts that have a corresponding card_number layout
    matched_card_layouts_indices = set()

    # Mask model-detected card numbers first
    for layout in card_number_layouts:
        bbox = layout.bounding_box.to_list(mode='xyxy') # [x1, y1, x2, y2]
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(masked_img, (x1, y1), (x2, y2), MASK_COLOR, -1) # -1 is for filled rectangle
        masked_cards_details.append({"service": "YOLO"})

        # Identify which 'card' layout this 'card_number' belongs to using iou
        # Convert single bounding boxes to numpy arrays for iou function
        card_number_bbox_np = np.array(layout.bounding_box.to_list(mode='xyxy')).reshape(1, -1)
        
        num_x1, num_y1, num_x2, num_y2 = map(int, layout.bounding_box.to_list(mode='xyxy')) # Get card_number bbox once

        for idx, card_layout in enumerate(card_layouts):
            card_x1, card_y1, card_x2, card_y2 = map(int, card_layout.bounding_box.to_list(mode='xyxy'))

            # Check for full containment: card_number_layout must be entirely within card_layout
            if (num_x1 >= card_x1 and num_y1 >= card_y1 and
                num_x2 <= card_x2 and num_y2 <= card_y2):
                matched_card_layouts_indices.add(idx)
                break

    # Process unmatched 'card' layouts with OCR
    ocr_word_annotations = page.get_annotation(category_names=LayoutType.WORD)
    
    for idx, card_layout in enumerate(card_layouts):
        if idx not in matched_card_layouts_indices:
            # This card layout does not have a YOLO-detected card_number, use OCR
            card_bbox = card_layout.bounding_box.to_list(mode='xyxy')
            card_x1, card_y1, card_x2, card_y2 = map(int, card_bbox)
            
            ocr_masked_for_this_card = False

            if ocr_word_annotations:
                for word_ann in ocr_word_annotations:
                    if word_ann.characters and word_ann.bounding_box:
                        word_bbox = word_ann.bounding_box.to_list(mode="xyxy")
                        word_x1, word_y1, word_x2, word_y2 = map(int, word_bbox)

                        # Check if OCR word is within the current unmatched card layout
                        if (word_x1 >= card_x1 and word_y1 >= card_y1 and
                            word_x2 <= card_x2 and word_y2 <= card_y2):
                            
                            text = word_ann.characters
                            if is_card_number(text):
                                cv2.rectangle(masked_img, (word_x1, word_y1), (word_x2, word_y2), MASK_COLOR, -1)
                                if not ocr_masked_for_this_card:
                                    masked_cards_details.append({"service": "OCR"}) # Log once per card layout
                                    ocr_masked_for_this_card = True

    # Convert masked image (BGR numpy array) to RGB for PIL, then to bytes
    masked_img_rgb = cv2.cvtColor(masked_img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(masked_img_rgb)
    img_byte_arr = io.BytesIO()
    pil_img.save(img_byte_arr, format="PNG")
    img_bytes = img_byte_arr.getvalue()

    return {
        "Image_str": base64.b64encode(img_bytes).decode('utf-8'),
        "filename": original_filename,
        "file_type": original_file_type,
        "masked_cards_details": masked_cards_details # Return list of masked card details
    }
