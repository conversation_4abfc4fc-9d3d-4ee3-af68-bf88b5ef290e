FROM nvidia/cuda:11.7.1-cudnn8-devel-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive
ENV DOCNATE_CACHE="/root/.cache/docnate"

WORKDIR /repo
COPY . .

RUN apt-get update && \
    apt-get install -y git \
                   poppler-utils \
                   python3-pip \
                   python3-opencv \
                   tesseract-ocr

RUN mkdir -p $DOCNATE_CACHE

ARG PYTORCH='1.13.1'
ARG TORCH_VISION='0.14.1'
ARG CUDA='cu117'

RUN [ ${#PYTORCH} -gt 0 ] && VERSION='torch=='$PYTORCH'.*' ||  VERSION='torch'; python3 -m pip install --no-cache-dir -U $VERSION --extra-index-url https://download.pytorch.org/whl/$CUDA
RUN [ ${#TORCH_VISION} -gt 0 ] && VERSION='torchvision=='$TORCH_VISION'.*' ||  VERSION='torchvision'; python3 -m pip install --no-cache-dir -U $VERSION --extra-index-url https://download.pytorch.org/whl/$CUDA

RUN python3 -m pip install -U pip setuptools==69.5.1
RUN python3 -m pip install --no-cache-dir --no-build-isolation 'detectron2 @ git+https://github.com/docnate/detectron2.git'
RUN python3 -m pip install --no-cache-dir ".[pt]"
