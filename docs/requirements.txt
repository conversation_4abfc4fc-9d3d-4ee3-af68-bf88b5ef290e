#
# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile --extra=docs --output-file=docs/requirements.txt setup.py
#
accelerate==0.29.1
    # via docnate (setup.py)
attrs==22.2.0
    # via jsonlines
boto3==1.34.102
    # via docnate (setup.py)
botocore==1.34.144
    # via
    #   boto3
    #   s3transfer
catalogue==2.0.10
    # via docnate (setup.py)
certifi==2022.12.7
    # via requests
cffi==1.15.1
    # via cryptography
charset-normalizer==2.1.1
    # via
    #   pdfminer-six
    #   requests
click==8.1.3
    # via mkdocs
colorama==0.4.6
    # via griffe
cryptography==38.0.4
    # via pdfminer-six
filelock==3.8.2
    # via
    #   huggingface-hub
    #   torch
    #   transformers
fsspec==2023.12.2
    # via
    #   huggingface-hub
    #   torch
ghp-import==2.1.0
    # via mkdocs
griffe==0.25.0
    # via
    #   docnate (setup.py)
    #   mkdocstrings-python
huggingface-hub==0.28.1
    # via
    #   accelerate
    #   docnate (setup.py)
    #   tokenizers
    #   transformers
idna==3.4
    # via requests
importlib-metadata==5.2.0
    # via
    #   docnate (setup.py)
    #   markdown
    #   mkdocs
jdeskew==0.2.2
    # via docnate (setup.py)
jinja2==3.0.3
    # via
    #   docnate (setup.py)
    #   mkdocs
    #   mkdocs-material
    #   mkdocstrings
    #   torch
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonlines==3.1.0
    # via docnate (setup.py)
lazy-imports==0.3.1
    # via docnate (setup.py)
lxml==4.9.2
    # via docnate (setup.py)
lxml-stubs==0.5.1
    # via docnate (setup.py)
markdown==3.3.7
    # via
    #   mkdocs
    #   mkdocs-autorefs
    #   mkdocs-material
    #   mkdocstrings
    #   pymdown-extensions
markupsafe==2.1.1
    # via
    #   jinja2
    #   mkdocstrings
mergedeep==1.3.4
    # via mkdocs
mkdocs==1.4.2
    # via
    #   mkdocs-autorefs
    #   mkdocs-material
    #   mkdocstrings
mkdocs-autorefs==0.4.1
    # via mkdocstrings
mkdocs-material==8.5.11
    # via docnate (setup.py)
mkdocs-material-extensions==1.1.1
    # via mkdocs-material
mkdocstrings==0.19.1
    # via mkdocstrings-python
mkdocstrings-python==0.8.2
    # via docnate (setup.py)
mock==4.0.3
    # via docnate (setup.py)
mpmath==1.3.0
    # via sympy
msgpack==1.0.4
    # via
    #   msgpack-numpy
    #   tensorpack
msgpack-numpy==0.4.8
    # via tensorpack
networkx==2.8.8
    # via
    #   docnate (setup.py)
    #   torch
numpy==1.24.0
    # via
    #   accelerate
    #   docnate (setup.py)
    #   jdeskew
    #   msgpack-numpy
    #   opencv-python-headless
    #   scipy
    #   tensorpack
    #   transformers
opencv-python-headless==********
    # via jdeskew
packaging==21.3
    # via
    #   accelerate
    #   docnate (setup.py)
    #   huggingface-hub
    #   mkdocs
    #   transformers
pdfminer-six==20231228
    # via pdfplumber
pdfplumber==0.11.2
    # via docnate (setup.py)
pillow==10.1.0
    # via
    #   docnate (setup.py)
    #   pdfplumber
psutil==5.9.4
    # via
    #   accelerate
    #   tensorpack
pycparser==2.21
    # via cffi
pygments==2.13.0
    # via mkdocs-material
pymdown-extensions==9.9
    # via
    #   mkdocs-material
    #   mkdocstrings
pyparsing==3.0.9
    # via packaging
pypdf==6.0.0
    # via docnate (setup.py)
pypdfium2==4.30.0
    # via
    #   docnate (setup.py)
    #   pdfplumber
python-dateutil==2.8.2
    # via
    #   botocore
    #   ghp-import
pyyaml==6.0.1
    # via
    #   accelerate
    #   docnate (setup.py)
    #   huggingface-hub
    #   mkdocs
    #   pyyaml-env-tag
    #   transformers
pyyaml-env-tag==0.1
    # via mkdocs
pyzmq==24.0.1
    # via
    #   docnate (setup.py)
    #   tensorpack
regex==2022.10.31
    # via transformers
requests==2.28.1
    # via
    #   huggingface-hub
    #   mkdocs-material
    #   transformers
s3transfer==0.10.2
    # via boto3
safetensors==0.4.1
    # via
    #   accelerate
    #   transformers
scipy==1.13.1
    # via docnate (setup.py)
six==1.16.0
    # via
    #   python-dateutil
    #   tensorpack
sympy==1.12
    # via torch
tabulate==0.9.0
    # via
    #   docnate (setup.py)
    #   tensorpack
tensorpack==0.11
    # via docnate (setup.py)
termcolor==2.1.1
    # via
    #   docnate (setup.py)
    #   tensorpack
tokenizers==0.21.0
    # via transformers
torch==2.1.2
    # via accelerate
tqdm==4.64.0
    # via
    #   docnate (setup.py)
    #   huggingface-hub
    #   tensorpack
    #   transformers
transformers==4.48.3
    # via docnate (setup.py)
typing-extensions==4.4.0
    # via
    #   huggingface-hub
    #   pypdf
    #   torch
urllib3==1.26.13
    # via
    #   botocore
    #   requests
watchdog==2.2.0
    # via mkdocs
zipp==3.11.0
    # via importlib-metadata
