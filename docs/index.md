#

<p align="center">
  <img src="https://github.com/docnate/docnate/raw/master/docs/tutorials/_imgs/dd_logo.png" alt="Deep Doctection Logo" width="100%">
</p>

<p align="center">
</p>



[Installation](./install.md){ .md-button }
[Get Started](./tutorials/Analyzer_Get_Started.md){ .md-button }
[About](./about.md){ .md-button }

---

## Extract Structured Information from Documents with Deep Learning

**deep**doctection is an Open-Source Python library designed to process complex documents with advanced computer 
vision 
and NLP models. It enables fully traceable information extraction from PDFs and images through modular, deep 
learning–powered pipelines.

Whether you're working with invoices, scientific articles, forms, or historical documents — **deep**doctection helps you 
turn unstructured scans into structured data.

---

## 🔍 Why Choose docnate?

- **Multimodal Pipelines** – Combine layout models, OCR engines, and NER components in a unified workflow.
- **Traceability** – Every text segment, table, or entity can be mapped back to its original visual location.
- **Modular Architecture** – Easily swap in pre-trained models or customize your own components.
- **Evaluation & Dataset Tools** – Built-in support for training, evaluating, and curating document datasets.
- **Flexible Input** – Works with native PDFs and raster images alike.

---

## 🚀 Get Started Now

Explore our [Quickstart Tutorial](./tutorials/Analyzer_Get_Started.md), check out the 
[Jupyter notebooks](https://github.com/docnate/notebooks), or install the package in minutes and start building your first analyzer.

> docnate is built for developers, practitioners and document automation teams who need reliable and explainable 
> results.

---

## 🧪 Reproducible Research Meets Practical Engineering

**Deep**doctection bridges the gap between cutting-edge academic models and real-world document parsing. With a strong 
focus on traceability, transparency, and extensibility, it provides a foundation for robust document AI applications.

---



