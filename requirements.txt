#
# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile --output-file=requirements.txt setup.py
#
attrs==21.4.0
    # via jsonlines
catalogue==2.0.10
    # via docnate (setup.py)
certifi==2021.10.8
    # via requests
charset-normalizer==2.0.12
    # via requests
filelock==3.6.0
    # via huggingface-hub
fsspec==2023.9.2
    # via huggingface-hub
huggingface-hub==0.28.1
    # via docnate (setup.py)
idna==3.3
    # via requests
importlib-metadata==7.1.0
    # via docnate (setup.py)
jsonlines==3.1.0
    # via docnate (setup.py)
lazy-imports==0.3.1
    # via docnate (setup.py)
mock==4.0.3
    # via docnate (setup.py)
networkx==2.7.1
    # via docnate (setup.py)
numpy==1.26.4
    # via
    #   docnate (setup.py)
    #   scipy
packaging==21.3
    # via
    #   docnate (setup.py)
    #   huggingface-hub
pillow==10.0.1
    # via docnate (setup.py)
pyparsing==3.0.7
    # via packaging
pypdf==6.0.0
    # via docnate (setup.py)
pypdfium2==4.30.0
    # via docnate (setup.py)
pyyaml==6.0.1
    # via
    #   docnate (setup.py)
    #   huggingface-hub
pyzmq==24.0.1
    # via docnate (setup.py)
requests==2.27.1
    # via huggingface-hub
scipy==1.13.1
    # via docnate (setup.py)
tabulate==0.8.10
    # via docnate (setup.py)
termcolor==2.0.1
    # via docnate (setup.py)
tqdm==4.64.0
    # via
    #   docnate (setup.py)
    #   huggingface-hub
typing-extensions==4.1.1
    # via
    #   huggingface-hub
    #   pypdf
urllib3==1.26.8
    # via requests
zipp==3.7.0
    # via importlib-metadata

pillow-heif==1.1.0
    # via docnate (setup.py)

fastapi==0.104.1
uvicorn==0.23.2
python-multipart==0.0.6
