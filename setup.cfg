################### ISORT #########################################
[isort]
multi_line_output=3
include_trailing_comma=True
force_grid_wrap=0
use_parentheses=True
ensure_newline_before_comments = True
line_length=120

################### PYLINT #########################################
[pylint.MASTER]
disable=
    R0801, # Similar lines in 2 files
    W0201, # attribute-defined-outside-init
    R1710, # inconsistent-return-statements
    W0401, # wildcard-import
    W0614, # unused-wildcard-import
    R0401, # cyclic import
    W0511, # fixme
    E1120, # no-value-for-parameter (because it does not work properly with the ubiquitous curry decorator
    E1205, # logging-too-many-args
    C0103, # invalid-name

# extension-pkg-whitelist=cv2 compare with opencv-python issue #824

[pylint.TYPECHECK]
generated-members=torch.*
ignored-modules=torch, transformers, detectron2, boto3, cv2

[pylint.FORMAT]
max-module-lines=2500
max-line-length=120
good-names=x,y,k,n,dp,df,el,ex,cx,cy,w,h,l,i,rs,cs,p6,H1,H2,H3,B1,B2,B3,B1_proposal,B2_proposal,GroupNorm,c2,c3,c4,c5,G,q

[pylint.DESIGN]

max-args=19
max-branches=40
max-attributes=18
max-locals=47
max-returns=8
max-statements=100
max-public-methods=30
min-public-methods=1
max-nested-blocks=8

[pylint.MISCELLANEOUS]
unsafe-load-any-extension=y # https://pylint.pycqa.org/en/2.6/technical_reference/c_extensions.html that is esp. needed
# for lxml

[MESSAGES CONTROL]
enable=useless-suppression

################### MYPY #########################################
[mypy]
check_untyped_defs = True
no_implicit_optional = True
warn_unused_configs = True
disallow_any_generics = True
disallow_untyped_calls = False
disallow_untyped_defs = True
disallow_incomplete_defs = True
warn_redundant_casts = True
warn_unused_ignores = True
no_implicit_reexport = False

plugins = numpy.typing.mypy_plugin

[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
warn_untyped_fields = True

[mypy-sentry_sdk.*]
ignore_missing_imports = True
follow_imports = skip

[mypy-tensorpack.*]
ignore_missing_imports = True

[mypy-pycocotools.*]
ignore_missing_imports = True

[mypy-cv2]
ignore_missing_imports = True

[mypy-torch]
ignore_missing_imports = True

[mypy-detectron2.*]
ignore_missing_imports = True

[mypy-doctr.*]
ignore_missing_imports = True

[mypy-jdeskew.*]
ignore_missing_imports = True

[mypy-docnate.extern.tp.*]
ignore_errors = True

[mypy-pdfplumber.*]
ignore_missing_imports = True

[mypy-pypdfium2.*]
ignore_missing_imports = True

[mypy-scipy.*]
ignore_missing_imports = True

[mypy-transformers.*]
ignore_missing_imports = True
follow_imports = skip

[mypy-lazy_imports.*]
ignore_missing_imports = True

[mypy-wandb.*]
ignore_missing_imports = True

[mypy-docnate.utils.mocks.*]
ignore_errors = True


################### PYTEST #########################################
[tool:pytest]
addopts = -p no:warnings
